/**
 * SAM - نظام إدارة شؤون الموظفين
 * Settings Management Module
 * وحدة إدارة الإعدادات
 */

class SettingsManager {
    constructor() {
        this.settings = Database.getSettings();
    }

    render() {
        if (!window.authManager.hasPermission('settings')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadSettings();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h2>
                </div>
            </div>

            <!-- Settings Tabs -->
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                        <i class="fas fa-building me-2"></i>معلومات الشركة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="working-hours-tab" data-bs-toggle="tab" data-bs-target="#working-hours" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>ساعات العمل
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab">
                        <i class="fas fa-user-clock me-2"></i>الحضور والانصراف
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                        <i class="fas fa-money-bill-wave me-2"></i>الرواتب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="leave-tab" data-bs-toggle="tab" data-bs-target="#leave" type="button" role="tab">
                        <i class="fas fa-calendar-alt me-2"></i>الإجازات
                    </button>
                </li>
            </ul>

            <!-- Settings Content -->
            <div class="tab-content" id="settingsTabContent">
                <!-- Company Settings -->
                <div class="tab-pane fade show active" id="company" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات الشركة</h5>
                        </div>
                        <div class="card-body">
                            <form id="companyForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم الشركة *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العملة *</label>
                                        <select class="form-select" name="currency" required>
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                            <option value="AED">درهم إماراتي (AED)</option>
                                            <option value="KWD">دينار كويتي (KWD)</option>
                                            <option value="QAR">ريال قطري (QAR)</option>
                                            <option value="BHD">دينار بحريني (BHD)</option>
                                            <option value="OMR">ريال عماني (OMR)</option>
                                            <option value="JOD">دينار أردني (JOD)</option>
                                            <option value="EGP">جنيه مصري (EGP)</option>
                                            <option value="SYP">ليرة سورية (SYP)</option>
                                            <option value="LBP">ليرة لبنانية (LBP)</option>
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="YER">ريال يمني (YER)</option>
                                            <option value="LYD">دينار ليبي (LYD)</option>
                                            <option value="TND">دينار تونسي (TND)</option>
                                            <option value="DZD">دينار جزائري (DZD)</option>
                                            <option value="MAD">درهم مغربي (MAD)</option>
                                            <option value="SDG">جنيه سوداني (SDG)</option>
                                            <option value="SOS">شلن صومالي (SOS)</option>
                                            <option value="DJF">فرنك جيبوتي (DJF)</option>
                                            <option value="KMF">فرنك قمري (KMF)</option>
                                            <option value="MRU">أوقية موريتانية (MRU)</option>
                                            <option value="GBP">جنيه إسترليني (GBP)</option>
                                            <option value="JPY">ين ياباني (JPY)</option>
                                            <option value="CNY">يوان صيني (CNY)</option>
                                            <option value="INR">روبية هندية (INR)</option>
                                            <option value="PKR">روبية باكستانية (PKR)</option>
                                            <option value="BDT">تاكا بنغلاديشية (BDT)</option>
                                            <option value="AFN">أفغاني أفغانستاني (AFN)</option>
                                            <option value="IRR">ريال إيراني (IRR)</option>
                                            <option value="TRY">ليرة تركية (TRY)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" name="currency_symbol" placeholder="ر.س">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم التسجيل التجاري</label>
                                        <input type="text" class="form-control" name="registration_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="tax_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" name="website">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea class="form-control" name="address" rows="3"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">وصف الشركة</label>
                                        <textarea class="form-control" name="description" rows="3"></textarea>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Working Hours Settings -->
                <div class="tab-pane fade" id="working-hours" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">ساعات العمل الافتراضية</h5>
                        </div>
                        <div class="card-body">
                            <form id="workingHoursForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت بداية العمل</label>
                                        <input type="time" class="form-control" name="start_time">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت نهاية العمل</label>
                                        <input type="time" class="form-control" name="end_time">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">مدة الراحة (دقيقة)</label>
                                        <input type="number" class="form-control" name="break_duration" min="0">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">أيام العمل</label>
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="sunday" id="sunday">
                                                    <label class="form-check-label" for="sunday">الأحد</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="monday" id="monday">
                                                    <label class="form-check-label" for="monday">الاثنين</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="tuesday" id="tuesday">
                                                    <label class="form-check-label" for="tuesday">الثلاثاء</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="wednesday" id="wednesday">
                                                    <label class="form-check-label" for="wednesday">الأربعاء</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="thursday" id="thursday">
                                                    <label class="form-check-label" for="thursday">الخميس</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="friday" id="friday">
                                                    <label class="form-check-label" for="friday">الجمعة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="saturday" id="saturday">
                                                    <label class="form-check-label" for="saturday">السبت</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Attendance Settings -->
                <div class="tab-pane fade" id="attendance" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الحضور والانصراف</h5>
                        </div>
                        <div class="card-body">
                            <form id="attendanceForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">فترة السماح للتأخير (دقيقة)</label>
                                        <input type="number" class="form-control" name="late_threshold" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">فترة السماح للخروج المبكر (دقيقة)</label>
                                        <input type="number" class="form-control" name="early_leave_threshold" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">معدل الساعات الإضافية</label>
                                        <input type="number" class="form-control" name="overtime_rate" step="0.1" min="1">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Payroll Settings -->
                <div class="tab-pane fade" id="payroll" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الرواتب</h5>
                        </div>
                        <div class="card-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">معدل التأمينات الاجتماعية (%)</label>
                                        <input type="number" class="form-control" name="social_insurance_rate" step="0.01" min="0" max="100">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحد الأدنى للراتب الخاضع للضريبة</label>
                                        <input type="number" class="form-control" name="tax_threshold" step="0.01" min="0">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Leave Settings -->
                <div class="tab-pane fade" id="leave" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الإجازات</h5>
                        </div>
                        <div class="card-body">
                            <form id="leaveForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الإجازة السنوية (يوم)</label>
                                        <input type="number" class="form-control" name="annual_leave_days" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الإجازة المرضية (يوم)</label>
                                        <input type="number" class="form-control" name="sick_leave_days" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجازة الأمومة (يوم)</label>
                                        <input type="number" class="form-control" name="maternity_leave_days" min="0">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Company form
        document.getElementById('companyForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCompanySettings();
        });

        // Working hours form
        document.getElementById('workingHoursForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWorkingHoursSettings();
        });

        // Attendance form
        document.getElementById('attendanceForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAttendanceSettings();
        });

        // Payroll form
        document.getElementById('payrollForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePayrollSettings();
        });

        // Leave form
        document.getElementById('leaveForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveLeaveSettings();
        });

        // Currency change event
        document.querySelector('select[name="currency"]').addEventListener('change', (e) => {
            this.updateCurrencySymbol(e.target.value);
        });
    }

    loadSettings() {
        try {
            // فرض إعادة تحميل الإعدادات من localStorage
            this.settings = Database.getSettings();
            console.log('Loaded settings:', this.settings); // للتشخيص

            // التحقق من وجود الإعدادات الأساسية
            if (!this.settings || typeof this.settings !== 'object') {
                console.warn('Settings object is invalid, creating default settings');
                this.settings = Database.getDefaultSettings();
                Database.saveSettings(this.settings);
            }

            // تحميل النماذج مع معالجة الأخطاء لكل نموذج على حدة
            this.safePopulateForm('company', () => this.populateCompanyForm());
            this.safePopulateForm('working_hours', () => this.populateWorkingHoursForm());
            this.safePopulateForm('attendance', () => this.populateAttendanceForm());
            this.safePopulateForm('payroll', () => this.populatePayrollForm());
            this.safePopulateForm('leave', () => this.populateLeaveForm());

        } catch (error) {
            console.error('Error loading settings:', error);
            window.samApp.showAlert('حدث خطأ في تحميل الإعدادات: ' + error.message, 'danger');

            // محاولة استخدام الإعدادات الافتراضية
            try {
                this.settings = Database.getDefaultSettings();
                this.populateCompanyForm();
                this.populateWorkingHoursForm();
                this.populateAttendanceForm();
                this.populatePayrollForm();
                this.populateLeaveForm();
            } catch (fallbackError) {
                console.error('Failed to load default settings:', fallbackError);
            }
        }
    }

    safePopulateForm(sectionName, populateFunction) {
        try {
            populateFunction();
            console.log(`Successfully populated ${sectionName} form`);
        } catch (error) {
            console.error(`Error populating ${sectionName} form:`, error);
            window.samApp.showAlert(`خطأ في تحميل إعدادات ${sectionName}: ${error.message}`, 'warning');
        }
    }

    populateCompanyForm() {
        const form = document.getElementById('companyForm');
        const company = this.settings.company || {};

        Object.keys(company).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = company[key] || '';
            }
        });
    }

    populateWorkingHoursForm() {
        const form = document.getElementById('workingHoursForm');
        const workingHours = this.settings.working_hours || {};

        // Populate time inputs
        form.querySelector('[name="start_time"]').value = workingHours.start_time || '08:00';
        form.querySelector('[name="end_time"]').value = workingHours.end_time || '17:00';
        form.querySelector('[name="break_duration"]').value = workingHours.break_duration || 60;

        // Populate working days checkboxes
        const workingDays = workingHours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        workingDays.forEach(day => {
            const checkbox = form.querySelector(`[value="${day}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    populateAttendanceForm() {
        const form = document.getElementById('attendanceForm');
        const attendance = this.settings.attendance || {};

        form.querySelector('[name="late_threshold"]').value = attendance.late_threshold || 15;
        form.querySelector('[name="early_leave_threshold"]').value = attendance.early_leave_threshold || 15;
        form.querySelector('[name="overtime_rate"]').value = attendance.overtime_rate || 1.5;
    }

    populatePayrollForm() {
        const form = document.getElementById('payrollForm');
        const payroll = this.settings.payroll || {};

        form.querySelector('[name="social_insurance_rate"]').value = (payroll.social_insurance_rate * 100) || 9;
        form.querySelector('[name="tax_threshold"]').value = payroll.tax_threshold || 3000;
    }

    populateLeaveForm() {
        const form = document.getElementById('leaveForm');
        const leave = this.settings.leave || {};

        form.querySelector('[name="annual_leave_days"]').value = leave.annual_leave_days || 21;
        form.querySelector('[name="sick_leave_days"]').value = leave.sick_leave_days || 30;
        form.querySelector('[name="maternity_leave_days"]').value = leave.maternity_leave_days || 70;
    }

    updateCurrencySymbol(currency) {
        const currencySymbols = {
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع',
            'JOD': 'د.أ',
            'EGP': 'ج.م',
            'SYP': 'ل.س',
            'LBP': 'ل.ل',
            'IQD': 'د.ع',
            'YER': 'ر.ي',
            'LYD': 'د.ل',
            'TND': 'د.ت',
            'DZD': 'د.ج',
            'MAD': 'د.م',
            'SDG': 'ج.س',
            'SOS': 'ش.ص',
            'DJF': 'ف.ج',
            'KMF': 'ف.ق',
            'MRU': 'أ.م',
            'GBP': '£',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'PKR': '₨',
            'BDT': '৳',
            'AFN': '؋',
            'IRR': 'ر.إ',
            'TRY': '₺'
        };

        const symbolInput = document.querySelector('input[name="currency_symbol"]');
        if (symbolInput && currencySymbols[currency]) {
            symbolInput.value = currencySymbols[currency];
        }
    }

    saveCompanySettings() {
        const form = document.getElementById('companyForm');
        if (!form) {
            window.samApp.showAlert('لم يتم العثور على نموذج إعدادات الشركة', 'danger');
            return;
        }

        const formData = new FormData(form);
        const companyData = Object.fromEntries(formData.entries());

        // التحقق من صحة البيانات
        if (!companyData.name || companyData.name.trim() === '') {
            window.samApp.showAlert('اسم الشركة مطلوب', 'warning');
            return;
        }

        try {
            console.log('Saving company data:', companyData);

            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تأكد من وجود قسم الشركة
            if (!this.settings.company) {
                this.settings.company = {};
            }

            // دمج البيانات الجديدة مع الموجودة
            this.settings.company = { ...this.settings.company, ...companyData };

            console.log('Updated settings:', this.settings);

            // حفظ الإعدادات مع التحقق من النجاح
            const result = Database.saveSettings(this.settings);
            console.log('Save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (savedSettings.company.name !== companyData.name) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // تحديث تنسيق العملة عالمياً
            if (companyData.currency) {
                this.updateGlobalCurrency(companyData.currency, companyData.currency_symbol);
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.forceReloadSettings();
            this.populateCompanyForm();

            window.samApp.showAlert('تم حفظ إعدادات الشركة بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving company settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.populateCompanyForm();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    saveWorkingHoursSettings() {
        const form = document.getElementById('workingHoursForm');
        if (!form) {
            window.samApp.showAlert('نموذج إعدادات ساعات العمل غير موجود', 'danger');
            return;
        }

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const startTime = formData.get('start_time');
        const endTime = formData.get('end_time');

        if (!startTime || !endTime) {
            window.samApp.showAlert('يرجى تحديد وقت بداية ونهاية العمل', 'warning');
            return;
        }

        const workingDays = Array.from(form.querySelectorAll('input[name="working_days"]:checked'))
            .map(checkbox => checkbox.value);

        if (workingDays.length === 0) {
            window.samApp.showAlert('يرجى تحديد أيام العمل', 'warning');
            return;
        }

        const workingHoursData = {
            start_time: startTime,
            end_time: endTime,
            break_duration: parseInt(formData.get('break_duration')) || 60,
            working_days: workingDays
        };

        try {
            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تحديث إعدادات ساعات العمل
            this.settings.working_hours = { ...this.settings.working_hours, ...workingHoursData };

            // حفظ الإعدادات
            const result = Database.saveSettings(this.settings);
            console.log('Working hours save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (savedSettings.working_hours.start_time !== startTime ||
                savedSettings.working_hours.end_time !== endTime) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.populateWorkingHoursForm();

            window.samApp.showAlert('تم حفظ إعدادات ساعات العمل بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving working hours settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.populateWorkingHoursForm();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    saveAttendanceSettings() {
        const form = document.getElementById('attendanceForm');
        if (!form) {
            window.samApp.showAlert('نموذج إعدادات الحضور غير موجود', 'danger');
            return;
        }

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const lateThreshold = parseInt(formData.get('late_threshold'));
        const earlyLeaveThreshold = parseInt(formData.get('early_leave_threshold'));
        const overtimeRate = parseFloat(formData.get('overtime_rate'));

        if (isNaN(lateThreshold) || lateThreshold < 0) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لحد التأخير', 'warning');
            return;
        }

        if (isNaN(earlyLeaveThreshold) || earlyLeaveThreshold < 0) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لحد الانصراف المبكر', 'warning');
            return;
        }

        if (isNaN(overtimeRate) || overtimeRate < 1) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لمعدل الساعات الإضافية (لا يقل عن 1)', 'warning');
            return;
        }

        const attendanceData = {
            late_threshold: lateThreshold,
            early_leave_threshold: earlyLeaveThreshold,
            overtime_rate: overtimeRate
        };

        try {
            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تحديث إعدادات الحضور
            this.settings.attendance = { ...this.settings.attendance, ...attendanceData };

            // حفظ الإعدادات
            const result = Database.saveSettings(this.settings);
            console.log('Attendance settings save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (savedSettings.attendance.late_threshold !== lateThreshold) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.populateAttendanceForm();

            window.samApp.showAlert('تم حفظ إعدادات الحضور بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving attendance settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.populateAttendanceForm();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    savePayrollSettings() {
        const form = document.getElementById('payrollForm');
        if (!form) {
            window.samApp.showAlert('نموذج إعدادات الرواتب غير موجود', 'danger');
            return;
        }

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const socialInsuranceRate = parseFloat(formData.get('social_insurance_rate'));
        const taxThreshold = parseFloat(formData.get('tax_threshold'));

        if (isNaN(socialInsuranceRate) || socialInsuranceRate < 0 || socialInsuranceRate > 100) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لمعدل التأمينات الاجتماعية (0-100%)', 'warning');
            return;
        }

        if (isNaN(taxThreshold) || taxThreshold < 0) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لحد الإعفاء الضريبي', 'warning');
            return;
        }

        const payrollData = {
            social_insurance_rate: socialInsuranceRate / 100, // Convert percentage to decimal
            tax_threshold: taxThreshold
        };

        try {
            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تحديث إعدادات الرواتب
            this.settings.payroll = { ...this.settings.payroll, ...payrollData };

            // حفظ الإعدادات
            const result = Database.saveSettings(this.settings);
            console.log('Payroll settings save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (Math.abs(savedSettings.payroll.social_insurance_rate - payrollData.social_insurance_rate) > 0.001) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.populatePayrollForm();

            window.samApp.showAlert('تم حفظ إعدادات الرواتب بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving payroll settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.populatePayrollForm();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    saveLeaveSettings() {
        const form = document.getElementById('leaveForm');
        if (!form) {
            window.samApp.showAlert('نموذج إعدادات الإجازات غير موجود', 'danger');
            return;
        }

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const annualLeaveDays = parseInt(formData.get('annual_leave_days'));
        const sickLeaveDays = parseInt(formData.get('sick_leave_days'));
        const maternityLeaveDays = parseInt(formData.get('maternity_leave_days'));

        if (isNaN(annualLeaveDays) || annualLeaveDays < 0 || annualLeaveDays > 365) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لأيام الإجازة السنوية (0-365)', 'warning');
            return;
        }

        if (isNaN(sickLeaveDays) || sickLeaveDays < 0 || sickLeaveDays > 365) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لأيام الإجازة المرضية (0-365)', 'warning');
            return;
        }

        if (isNaN(maternityLeaveDays) || maternityLeaveDays < 0 || maternityLeaveDays > 365) {
            window.samApp.showAlert('يرجى إدخال قيمة صحيحة لأيام إجازة الأمومة (0-365)', 'warning');
            return;
        }

        const leaveData = {
            annual_leave_days: annualLeaveDays,
            sick_leave_days: sickLeaveDays,
            maternity_leave_days: maternityLeaveDays
        };

        try {
            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تحديث إعدادات الإجازات
            this.settings.leave = { ...this.settings.leave, ...leaveData };

            // حفظ الإعدادات
            const result = Database.saveSettings(this.settings);
            console.log('Leave settings save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (savedSettings.leave.annual_leave_days !== annualLeaveDays) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.populateLeaveForm();

            window.samApp.showAlert('تم حفظ إعدادات الإجازات بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving leave settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.populateLeaveForm();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    updateGlobalCurrency(currency, symbol) {
        // Update the formatCurrency function in the main app
        if (window.samApp) {
            window.samApp.currency = currency;
            window.samApp.currencySymbol = symbol;

            // Override the formatCurrency method
            window.samApp.formatCurrency = function(amount) {
                if (symbol) {
                    return `${parseFloat(amount).toLocaleString('ar-SA')} ${symbol}`;
                } else {
                    return new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: currency
                    }).format(amount);
                }
            };
        }
    }

    forceReloadSettings() {
        try {
            console.log('Force reloading settings from localStorage...');

            // مسح أي كاش محلي
            this.settings = null;

            // إعادة تحميل مباشرة من localStorage
            const rawSettings = localStorage.getItem('sam_settings');
            if (rawSettings) {
                this.settings = JSON.parse(rawSettings);
                console.log('Settings force reloaded:', this.settings);
            } else {
                console.warn('No settings found in localStorage');
                this.settings = Database.getDefaultSettings();
                Database.saveSettings(this.settings);
            }

            return true;
        } catch (error) {
            console.error('Error force reloading settings:', error);
            return false;
        }
    }

    // دالة لإعادة تحميل جميع الإعدادات من قاعدة البيانات
    reloadAllSettings() {
        try {
            console.log('Reloading all settings from database...');

            // إعادة تحميل الإعدادات من قاعدة البيانات
            this.settings = Database.getSettings();

            // إعادة تحميل جميع النماذج
            this.populateCompanyForm();
            this.populateWorkingHoursForm();
            this.populateAttendanceForm();
            this.populatePayrollForm();
            this.populateLeaveForm();

            console.log('All settings reloaded successfully');
            return true;

        } catch (error) {
            console.error('Error reloading settings:', error);
            window.samApp.showAlert('حدث خطأ في إعادة تحميل الإعدادات: ' + error.message, 'danger');
            return false;
        }
    }

    // دالة للتحقق من تطابق الإعدادات المحفوظة مع النموذج
    validateSavedSettings(sectionName, expectedData) {
        try {
            const savedSettings = Database.getSettings();
            const savedSection = savedSettings[sectionName];

            if (!savedSection) {
                console.warn(`Section ${sectionName} not found in saved settings`);
                return false;
            }

            // التحقق من تطابق البيانات الأساسية
            for (const key in expectedData) {
                if (savedSection[key] !== expectedData[key]) {
                    console.warn(`Mismatch in ${sectionName}.${key}: expected ${expectedData[key]}, got ${savedSection[key]}`);
                    return false;
                }
            }

            return true;

        } catch (error) {
            console.error('Error validating saved settings:', error);
            return false;
        }
    }
}

// Global reference
let settingsManager;
