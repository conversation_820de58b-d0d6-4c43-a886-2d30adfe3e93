/**
 * SAM - نظام إدارة شؤون الموظفين
 * Settings Management Module
 * وحدة إدارة الإعدادات
 */

class SettingsManager {
    constructor() {
        this.settings = Database.getSettings();
    }

    render() {
        if (!window.authManager.hasPermission('settings')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();
        this.bindEvents();
        this.loadSettings();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h2>
                </div>
            </div>

            <!-- Settings Tabs -->
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                        <i class="fas fa-building me-2"></i>معلومات الشركة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="working-hours-tab" data-bs-toggle="tab" data-bs-target="#working-hours" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>ساعات العمل
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab">
                        <i class="fas fa-user-clock me-2"></i>الحضور والانصراف
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                        <i class="fas fa-money-bill-wave me-2"></i>الرواتب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="leave-tab" data-bs-toggle="tab" data-bs-target="#leave" type="button" role="tab">
                        <i class="fas fa-calendar-alt me-2"></i>الإجازات
                    </button>
                </li>
            </ul>

            <!-- Settings Content -->
            <div class="tab-content" id="settingsTabContent">
                <!-- Company Settings -->
                <div class="tab-pane fade show active" id="company" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات الشركة</h5>
                        </div>
                        <div class="card-body">
                            <form id="companyForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم الشركة *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العملة *</label>
                                        <select class="form-select" name="currency" required>
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                            <option value="AED">درهم إماراتي (AED)</option>
                                            <option value="KWD">دينار كويتي (KWD)</option>
                                            <option value="QAR">ريال قطري (QAR)</option>
                                            <option value="BHD">دينار بحريني (BHD)</option>
                                            <option value="OMR">ريال عماني (OMR)</option>
                                            <option value="JOD">دينار أردني (JOD)</option>
                                            <option value="EGP">جنيه مصري (EGP)</option>
                                            <option value="SYP">ليرة سورية (SYP)</option>
                                            <option value="LBP">ليرة لبنانية (LBP)</option>
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="YER">ريال يمني (YER)</option>
                                            <option value="LYD">دينار ليبي (LYD)</option>
                                            <option value="TND">دينار تونسي (TND)</option>
                                            <option value="DZD">دينار جزائري (DZD)</option>
                                            <option value="MAD">درهم مغربي (MAD)</option>
                                            <option value="SDG">جنيه سوداني (SDG)</option>
                                            <option value="SOS">شلن صومالي (SOS)</option>
                                            <option value="DJF">فرنك جيبوتي (DJF)</option>
                                            <option value="KMF">فرنك قمري (KMF)</option>
                                            <option value="MRU">أوقية موريتانية (MRU)</option>
                                            <option value="GBP">جنيه إسترليني (GBP)</option>
                                            <option value="JPY">ين ياباني (JPY)</option>
                                            <option value="CNY">يوان صيني (CNY)</option>
                                            <option value="INR">روبية هندية (INR)</option>
                                            <option value="PKR">روبية باكستانية (PKR)</option>
                                            <option value="BDT">تاكا بنغلاديشية (BDT)</option>
                                            <option value="AFN">أفغاني أفغانستاني (AFN)</option>
                                            <option value="IRR">ريال إيراني (IRR)</option>
                                            <option value="TRY">ليرة تركية (TRY)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" name="currency_symbol" placeholder="ر.س">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم التسجيل التجاري</label>
                                        <input type="text" class="form-control" name="registration_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="tax_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" name="website">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea class="form-control" name="address" rows="3"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">وصف الشركة</label>
                                        <textarea class="form-control" name="description" rows="3"></textarea>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Working Hours Settings -->
                <div class="tab-pane fade" id="working-hours" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">ساعات العمل الافتراضية</h5>
                        </div>
                        <div class="card-body">
                            <form id="workingHoursForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت بداية العمل</label>
                                        <input type="time" class="form-control" name="start_time">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">وقت نهاية العمل</label>
                                        <input type="time" class="form-control" name="end_time">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">مدة الراحة (دقيقة)</label>
                                        <input type="number" class="form-control" name="break_duration" min="0">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">أيام العمل</label>
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="sunday" id="sunday">
                                                    <label class="form-check-label" for="sunday">الأحد</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="monday" id="monday">
                                                    <label class="form-check-label" for="monday">الاثنين</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="tuesday" id="tuesday">
                                                    <label class="form-check-label" for="tuesday">الثلاثاء</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="wednesday" id="wednesday">
                                                    <label class="form-check-label" for="wednesday">الأربعاء</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="thursday" id="thursday">
                                                    <label class="form-check-label" for="thursday">الخميس</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="friday" id="friday">
                                                    <label class="form-check-label" for="friday">الجمعة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="working_days" value="saturday" id="saturday">
                                                    <label class="form-check-label" for="saturday">السبت</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Attendance Settings -->
                <div class="tab-pane fade" id="attendance" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الحضور والانصراف</h5>
                        </div>
                        <div class="card-body">
                            <form id="attendanceForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">فترة السماح للتأخير (دقيقة)</label>
                                        <input type="number" class="form-control" name="late_threshold" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">فترة السماح للخروج المبكر (دقيقة)</label>
                                        <input type="number" class="form-control" name="early_leave_threshold" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">معدل الساعات الإضافية</label>
                                        <input type="number" class="form-control" name="overtime_rate" step="0.1" min="1">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Payroll Settings -->
                <div class="tab-pane fade" id="payroll" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الرواتب</h5>
                        </div>
                        <div class="card-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">معدل التأمينات الاجتماعية (%)</label>
                                        <input type="number" class="form-control" name="social_insurance_rate" step="0.01" min="0" max="100">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحد الأدنى للراتب الخاضع للضريبة</label>
                                        <input type="number" class="form-control" name="tax_threshold" step="0.01" min="0">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Leave Settings -->
                <div class="tab-pane fade" id="leave" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات الإجازات</h5>
                        </div>
                        <div class="card-body">
                            <form id="leaveForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الإجازة السنوية (يوم)</label>
                                        <input type="number" class="form-control" name="annual_leave_days" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الإجازة المرضية (يوم)</label>
                                        <input type="number" class="form-control" name="sick_leave_days" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجازة الأمومة (يوم)</label>
                                        <input type="number" class="form-control" name="maternity_leave_days" min="0">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Company form
        document.getElementById('companyForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCompanySettings();
        });

        // Working hours form
        document.getElementById('workingHoursForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWorkingHoursSettings();
        });

        // Attendance form
        document.getElementById('attendanceForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAttendanceSettings();
        });

        // Payroll form
        document.getElementById('payrollForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePayrollSettings();
        });

        // Leave form
        document.getElementById('leaveForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveLeaveSettings();
        });

        // Currency change event
        document.querySelector('select[name="currency"]').addEventListener('change', (e) => {
            this.updateCurrencySymbol(e.target.value);
        });
    }

    loadSettings() {
        try {
            this.settings = Database.getSettings();
            console.log('Loaded settings:', this.settings); // للتشخيص
            this.populateCompanyForm();
            this.populateWorkingHoursForm();
            this.populateAttendanceForm();
            this.populatePayrollForm();
            this.populateLeaveForm();
        } catch (error) {
            console.error('Error loading settings:', error);
            window.samApp.showAlert('حدث خطأ في تحميل الإعدادات: ' + error.message, 'danger');
        }
    }

    populateCompanyForm() {
        const form = document.getElementById('companyForm');
        const company = this.settings.company || {};

        Object.keys(company).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = company[key] || '';
            }
        });
    }

    populateWorkingHoursForm() {
        const form = document.getElementById('workingHoursForm');
        const workingHours = this.settings.working_hours || {};

        // Populate time inputs
        form.querySelector('[name="start_time"]').value = workingHours.start_time || '08:00';
        form.querySelector('[name="end_time"]').value = workingHours.end_time || '17:00';
        form.querySelector('[name="break_duration"]').value = workingHours.break_duration || 60;

        // Populate working days checkboxes
        const workingDays = workingHours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        workingDays.forEach(day => {
            const checkbox = form.querySelector(`[value="${day}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    populateAttendanceForm() {
        const form = document.getElementById('attendanceForm');
        const attendance = this.settings.attendance || {};

        form.querySelector('[name="late_threshold"]').value = attendance.late_threshold || 15;
        form.querySelector('[name="early_leave_threshold"]').value = attendance.early_leave_threshold || 15;
        form.querySelector('[name="overtime_rate"]').value = attendance.overtime_rate || 1.5;
    }

    populatePayrollForm() {
        const form = document.getElementById('payrollForm');
        const payroll = this.settings.payroll || {};

        form.querySelector('[name="social_insurance_rate"]').value = (payroll.social_insurance_rate * 100) || 9;
        form.querySelector('[name="tax_threshold"]').value = payroll.tax_threshold || 3000;
    }

    populateLeaveForm() {
        const form = document.getElementById('leaveForm');
        const leave = this.settings.leave || {};

        form.querySelector('[name="annual_leave_days"]').value = leave.annual_leave_days || 21;
        form.querySelector('[name="sick_leave_days"]').value = leave.sick_leave_days || 30;
        form.querySelector('[name="maternity_leave_days"]').value = leave.maternity_leave_days || 70;
    }

    updateCurrencySymbol(currency) {
        const currencySymbols = {
            'SAR': 'ر.س',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع',
            'JOD': 'د.أ',
            'EGP': 'ج.م',
            'SYP': 'ل.س',
            'LBP': 'ل.ل',
            'IQD': 'د.ع',
            'YER': 'ر.ي',
            'LYD': 'د.ل',
            'TND': 'د.ت',
            'DZD': 'د.ج',
            'MAD': 'د.م',
            'SDG': 'ج.س',
            'SOS': 'ش.ص',
            'DJF': 'ف.ج',
            'KMF': 'ف.ق',
            'MRU': 'أ.م',
            'GBP': '£',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'PKR': '₨',
            'BDT': '৳',
            'AFN': '؋',
            'IRR': 'ر.إ',
            'TRY': '₺'
        };

        const symbolInput = document.querySelector('input[name="currency_symbol"]');
        if (symbolInput && currencySymbols[currency]) {
            symbolInput.value = currencySymbols[currency];
        }
    }

    saveCompanySettings() {
        const form = document.getElementById('companyForm');
        if (!form) {
            window.samApp.showAlert('لم يتم العثور على نموذج إعدادات الشركة', 'danger');
            return;
        }

        const formData = new FormData(form);
        const companyData = Object.fromEntries(formData.entries());

        // التحقق من صحة البيانات
        if (!companyData.name || companyData.name.trim() === '') {
            window.samApp.showAlert('اسم الشركة مطلوب', 'warning');
            return;
        }

        try {
            console.log('Saving company data:', companyData);

            // إعادة تحميل الإعدادات الحالية لضمان الحصول على أحدث نسخة
            this.settings = Database.getSettings();

            // تأكد من وجود قسم الشركة
            if (!this.settings.company) {
                this.settings.company = {};
            }

            // دمج البيانات الجديدة مع الموجودة
            this.settings.company = { ...this.settings.company, ...companyData };

            console.log('Updated settings:', this.settings);

            // حفظ الإعدادات مع التحقق من النجاح
            const result = Database.saveSettings(this.settings);
            console.log('Save result:', result);

            // التحقق من نجاح الحفظ عبر إعادة قراءة الإعدادات
            const savedSettings = Database.getSettings();
            if (savedSettings.company.name !== companyData.name) {
                throw new Error('فشل في التحقق من حفظ البيانات');
            }

            // تحديث تنسيق العملة عالمياً
            if (companyData.currency) {
                this.updateGlobalCurrency(companyData.currency, companyData.currency_symbol);
            }

            // إعادة تحميل الإعدادات في النموذج للتأكد من التحديث
            this.loadCompanySettings();

            window.samApp.showAlert('تم حفظ إعدادات الشركة بنجاح وتم التحقق من الحفظ', 'success');

        } catch (error) {
            console.error('Error saving company settings:', error);

            // محاولة استعادة الإعدادات من النسخة الاحتياطية
            try {
                this.settings = Database.getSettings();
                this.loadCompanySettings();
                window.samApp.showAlert('حدث خطأ في الحفظ، تم استعادة الإعدادات السابقة: ' + error.message, 'danger');
            } catch (restoreError) {
                window.samApp.showAlert('حدث خطأ خطير في حفظ الإعدادات: ' + error.message, 'danger');
            }
        }
    }

    saveWorkingHoursSettings() {
        const form = document.getElementById('workingHoursForm');
        const formData = new FormData(form);

        const workingDays = Array.from(form.querySelectorAll('input[name="working_days"]:checked'))
            .map(checkbox => checkbox.value);

        const workingHoursData = {
            start_time: formData.get('start_time'),
            end_time: formData.get('end_time'),
            break_duration: parseInt(formData.get('break_duration')) || 60,
            working_days: workingDays
        };

        try {
            this.settings.working_hours = workingHoursData;
            Database.saveSettings(this.settings);
            window.samApp.showAlert('تم حفظ إعدادات ساعات العمل بنجاح', 'success');
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    saveAttendanceSettings() {
        const form = document.getElementById('attendanceForm');
        const formData = new FormData(form);

        const attendanceData = {
            late_threshold: parseInt(formData.get('late_threshold')) || 15,
            early_leave_threshold: parseInt(formData.get('early_leave_threshold')) || 15,
            overtime_rate: parseFloat(formData.get('overtime_rate')) || 1.5
        };

        try {
            this.settings.attendance = attendanceData;
            Database.saveSettings(this.settings);
            window.samApp.showAlert('تم حفظ إعدادات الحضور بنجاح', 'success');
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    savePayrollSettings() {
        const form = document.getElementById('payrollForm');
        const formData = new FormData(form);

        const payrollData = {
            social_insurance_rate: (parseFloat(formData.get('social_insurance_rate')) || 9) / 100,
            tax_threshold: parseFloat(formData.get('tax_threshold')) || 3000
        };

        try {
            this.settings.payroll = { ...this.settings.payroll, ...payrollData };
            Database.saveSettings(this.settings);
            window.samApp.showAlert('تم حفظ إعدادات الرواتب بنجاح', 'success');
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    saveLeaveSettings() {
        const form = document.getElementById('leaveForm');
        const formData = new FormData(form);

        const leaveData = {
            annual_leave_days: parseInt(formData.get('annual_leave_days')) || 21,
            sick_leave_days: parseInt(formData.get('sick_leave_days')) || 30,
            maternity_leave_days: parseInt(formData.get('maternity_leave_days')) || 70
        };

        try {
            this.settings.leave = leaveData;
            Database.saveSettings(this.settings);
            window.samApp.showAlert('تم حفظ إعدادات الإجازات بنجاح', 'success');
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    updateGlobalCurrency(currency, symbol) {
        // Update the formatCurrency function in the main app
        if (window.samApp) {
            window.samApp.currency = currency;
            window.samApp.currencySymbol = symbol;

            // Override the formatCurrency method
            window.samApp.formatCurrency = function(amount) {
                if (symbol) {
                    return `${parseFloat(amount).toLocaleString('ar-SA')} ${symbol}`;
                } else {
                    return new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: currency
                    }).format(amount);
                }
            };
        }
    }
}

// Global reference
let settingsManager;
