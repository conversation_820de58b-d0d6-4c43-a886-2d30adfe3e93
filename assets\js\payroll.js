/**
 * SAM - نظام إدارة شؤون الموظفين
 * Payroll Management Module
 * وحدة إدارة الرواتب
 */

class PayrollManager {
    constructor() {
        this.currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
        this.selectedEmployee = '';
        this.selectedStatus = '';
        this.selectedPayroll = null;
        this.payrollDetails = null;
        this.lastCalculation = null;
        this.autoRefreshInterval = null;
    }

    render() {
        if (!window.authManager.hasPermission('payroll')) {
            window.authManager.showPermissionError();
            return;
        }

        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getMainHTML();

        // إضافة تأثير الانتقال
        contentArea.classList.add('fade-in');

        this.bindEvents();
        this.loadPayrollData();
    }

    getMainHTML() {
        return `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-money-bill-wave me-2"></i>
                            إدارة الرواتب
                        </h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" id="generatePayrollBtn">
                                <i class="fas fa-calculator me-2"></i>
                                حساب الرواتب
                            </button>
                            <button class="btn btn-info" id="showTotalSummaryBtn">
                                <i class="fas fa-chart-pie me-2"></i>
                                إجمالي الرواتب
                            </button>
                            <button class="btn btn-warning" id="autoUpdateAllBtn">
                                <i class="fas fa-sync-alt me-2"></i>
                                تحديث تلقائي للكل
                            </button>
                            <button class="btn btn-success" id="addPayrollBtn">
                                <i class="fas fa-plus me-2"></i>
                                إضافة راتب
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payroll Summary -->
            <div class="row mb-4 payroll-stats">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="totalPayroll">0</h4>
                                    <p class="mb-0">إجمالي الرواتب</p>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="paidCount">0</h4>
                                    <p class="mb-0">مدفوعة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="pendingCount">0</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 id="employeeCount">0</h4>
                                    <p class="mb-0">عدد الموظفين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">الشهر</label>
                    <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الموظف</label>
                    <select class="form-select" id="employeeFilter">
                        <option value="">جميع الموظفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="paid">مدفوعة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary flex-fill" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-outline-warning" id="recalculateBtn" title="إعادة حساب جميع الرواتب">
                            <i class="fas fa-calculator"></i> إعادة حساب
                        </button>
                        <button class="btn btn-outline-success" id="exportPayrollBtn">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-info" id="printPayrollBtn">
                            <i class="fas fa-print"></i> طباعة إجمالية
                        </button>
                    </div>
                </div>
            </div>

            <!-- Payroll Table -->
            <div class="card payroll-table">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        كشوف الرواتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>الشهر</th>
                                    <th>الراتب الأساسي</th>
                                    <th>البدلات</th>
                                    <th>الحوافز</th>
                                    <th>الخصومات</th>
                                    <th>صافي الراتب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payrollTableBody">
                                <!-- Payroll records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payroll Modal -->
            <div class="modal fade payroll-modal" id="payrollModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="payrollModalTitle">إضافة راتب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="payrollForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الشهر *</label>
                                        <input type="month" class="form-control" name="month" required>
                                    </div>
                                </div>

                                <!-- Basic Salary Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-money-bill me-2"></i>
                                            الراتب الأساسي
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">أيام العمل</label>
                                        <input type="number" class="form-control" name="working_days" min="0" max="31">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">أيام الحضور</label>
                                        <input type="number" class="form-control" name="attendance_days" min="0" max="31">
                                    </div>
                                </div>

                                <!-- Allowances Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-success mb-3">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            البدلات والإضافات
                                        </h6>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل سكن</label>
                                        <input type="number" class="form-control" name="housing_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل مواصلات</label>
                                        <input type="number" class="form-control" name="transport_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدل طعام</label>
                                        <input type="number" class="form-control" name="food_allowance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">بدلات أخرى</label>
                                        <input type="number" class="form-control" name="other_allowances" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Bonuses Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-info mb-3">
                                            <i class="fas fa-gift me-2"></i>
                                            الحوافز والمكافآت
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">حافز الأداء</label>
                                        <input type="number" class="form-control" name="performance_bonus" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">ساعات إضافية</label>
                                        <input type="number" class="form-control" name="overtime_hours" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">حوافز أخرى</label>
                                        <input type="number" class="form-control" name="other_bonuses" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Deductions Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-danger mb-3">
                                            <i class="fas fa-minus-circle me-2"></i>
                                            الخصومات والاستقطاعات
                                        </h6>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">التأمينات الاجتماعية</label>
                                        <input type="number" class="form-control" name="social_insurance" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">ضريبة الدخل</label>
                                        <input type="number" class="form-control" name="income_tax" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">السلف</label>
                                        <input type="number" class="form-control" name="advances" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصم التأخير</label>
                                        <input type="number" class="form-control" name="lateness_deduction" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصم الغياب</label>
                                        <input type="number" class="form-control" name="absence_deduction" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">خصومات أخرى</label>
                                        <input type="number" class="form-control" name="other_deductions" step="0.01" min="0">
                                    </div>
                                </div>

                                <!-- Summary Section -->
                                <div class="payroll-section">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-calculator me-2"></i>
                                            ملخص الراتب
                                        </h6>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجمالي الاستحقاقات</label>
                                        <input type="number" class="form-control text-success fw-bold" name="gross_salary" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">إجمالي الخصومات</label>
                                        <input type="number" class="form-control text-danger fw-bold" name="total_deductions" step="0.01" readonly>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">صافي الراتب</label>
                                        <input type="number" class="form-control text-primary fw-bold" name="net_salary" step="0.01" readonly>
                                    </div>
                                </div>

                                <!-- Status and Notes -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="pending">في الانتظار</option>
                                            <option value="paid">مدفوعة</option>
                                            <option value="cancelled">ملغية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الدفع</label>
                                        <input type="date" class="form-control" name="payment_date">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="calculateBtn">حساب</button>
                            <button type="button" class="btn btn-success" id="savePayrollBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Payroll Summary Modal -->
            <div class="modal fade" id="totalSummaryModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-pie me-2"></i>
                                إجمالي الرواتب الشامل - <span id="summaryMonthTitle">${this.currentMonth}</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Summary Cards -->
                            <div class="row mb-4" id="totalSummaryCards">
                                <!-- Cards will be populated here -->
                            </div>

                            <!-- Detailed Breakdown -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-plus-circle me-2"></i>
                                                تفصيل الاستحقاقات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="allowancesBreakdown">
                                                <!-- Allowances breakdown will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-minus-circle me-2"></i>
                                                تفصيل الخصومات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="deductionsBreakdown">
                                                <!-- Deductions breakdown will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Details Table -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        تفاصيل رواتب الموظفين
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>الموظف</th>
                                                    <th>القسم</th>
                                                    <th>الراتب الأساسي</th>
                                                    <th>البدلات</th>
                                                    <th>الحوافز</th>
                                                    <th>إجمالي الاستحقاقات</th>
                                                    <th>الخصومات</th>
                                                    <th>صافي الراتب</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody id="employeeDetailsTable">
                                                <!-- Employee details will be populated here -->
                                            </tbody>
                                            <tfoot class="table-warning">
                                                <tr id="totalSummaryRow">
                                                    <!-- Total row will be populated here -->
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-primary" id="exportTotalSummaryBtn">
                                <i class="fas fa-download me-2"></i>
                                تصدير التقرير
                            </button>
                            <button type="button" class="btn btn-outline-info" id="printTotalSummaryBtn">
                                <i class="fas fa-print me-2"></i>
                                طباعة التقرير
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Generate payroll button
        document.getElementById('generatePayrollBtn').addEventListener('click', () => {
            this.generateMonthlyPayroll();
        });

        // Add payroll button
        document.getElementById('addPayrollBtn').addEventListener('click', () => {
            this.showPayrollModal();
        });

        // Auto update all payrolls button
        const autoUpdateAllBtn = document.getElementById('autoUpdateAllBtn');
        if (autoUpdateAllBtn) {
            autoUpdateAllBtn.addEventListener('click', () => {
                this.handleAutoUpdateAll();
            });
        }

        // Show total summary button
        const showTotalSummaryBtn = document.getElementById('showTotalSummaryBtn');
        if (showTotalSummaryBtn) {
            showTotalSummaryBtn.addEventListener('click', () => {
                console.log('Show total summary button clicked'); // للتشخيص
                try {
                    this.showTotalSummary();
                } catch (error) {
                    console.error('Error in showTotalSummary:', error);
                    window.samApp.showAlert('حدث خطأ في عرض إجمالي الرواتب: ' + error.message, 'danger');
                }
            });
            console.log('Show total summary button event bound successfully');
        } else {
            console.error('Show total summary button not found');
        }

        // Filter events with auto-refresh
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.currentMonth = e.target.value;
            this.loadPayrollData();
        });

        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadPayrollData();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.selectedStatus = e.target.value;
            this.loadPayrollData();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            const btn = document.getElementById('refreshBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            btn.disabled = true;

            setTimeout(() => {
                this.loadPayrollData();
                btn.innerHTML = originalText;
                btn.disabled = false;
                window.samApp.showAlert('تم تحديث البيانات بنجاح', 'success');
            }, 500);
        });

        // Recalculate button
        document.getElementById('recalculateBtn').addEventListener('click', () => {
            if (confirm('هل أنت متأكد من إعادة حساب جميع كشوف الرواتب للشهر المحدد؟')) {
                this.recalculateMonthlyPayrolls();
            }
        });

        // Export button
        document.getElementById('exportPayrollBtn').addEventListener('click', () => {
            this.exportPayroll();
        });

        // Print button
        document.getElementById('printPayrollBtn').addEventListener('click', () => {
            this.printPayrollSummary();
        });

        // Save payroll
        document.getElementById('savePayrollBtn').addEventListener('click', () => {
            this.savePayroll();
        });

        // Calculate button
        document.getElementById('calculateBtn').addEventListener('click', () => {
            console.log('Calculate button clicked'); // للتشخيص
            this.calculatePayroll();

            // إظهار رسالة تأكيد
            const calc = this.lastCalculation;
            if (calc) {
                window.samApp.showAlert(
                    `تم حساب الراتب بنجاح - صافي الراتب: ${window.samApp.formatCurrency(calc.netSalary)}`,
                    'success'
                );
            }
        });

        // Auto-calculate when values change
        this.bindCalculationEvents();

        // Auto-refresh every 30 seconds to show any updates
        this.autoRefreshInterval = setInterval(() => {
            // تحديث صامت للبيانات دون إظهار مؤشر التحميل
            this.updatePayrollStats();

            // تحديث الجدول فقط إذا لم يكن هناك نموذج مفتوح
            const modal = document.getElementById('payrollModal');
            if (!modal || !modal.classList.contains('show')) {
                this.loadPayrollTable();
            }
        }, 30000);

        // Listen for attendance updates
        this.setupAttendanceListener();
    }

    bindCalculationEvents() {
        const form = document.getElementById('payrollForm');
        if (!form) return;

        const inputs = form.querySelectorAll('input[type="number"]');

        inputs.forEach(input => {
            if (!input.readOnly) {
                // إضافة أحداث متعددة للحساب التلقائي
                ['input', 'change', 'blur'].forEach(eventType => {
                    input.addEventListener(eventType, () => {
                        // تأخير قصير لتجنب الحسابات المتكررة
                        clearTimeout(this.calculationTimeout);
                        this.calculationTimeout = setTimeout(() => {
                            this.calculatePayroll();
                        }, 300);
                    });
                });

                // إضافة تنسيق للأرقام عند فقدان التركيز
                input.addEventListener('blur', (e) => {
                    const value = parseFloat(e.target.value);
                    if (!isNaN(value) && value >= 0) {
                        e.target.value = value.toFixed(2);
                    } else if (isNaN(value) || value < 0) {
                        e.target.value = '0.00';
                        window.samApp.showAlert('يجب أن تكون القيمة رقماً موجباً', 'warning');
                    }
                });

                // إضافة تلميحات بصرية عند التركيز
                input.addEventListener('focus', (e) => {
                    e.target.style.borderColor = '#007bff';
                    e.target.style.boxShadow = '0 0 0 0.2rem rgba(0, 123, 255, 0.25)';
                });

                input.addEventListener('blur', (e) => {
                    e.target.style.borderColor = '';
                    e.target.style.boxShadow = '';
                });
            }
        });

        // Employee selection change
        const employeeSelect = form.querySelector('select[name="employee_id"]');
        if (employeeSelect) {
            employeeSelect.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.loadEmployeeData(e.target.value);
                } else {
                    // إعادة تعيين النموذج إذا لم يتم اختيار موظف
                    this.resetPayrollForm();
                }
            });
        }

        // Month change event
        const monthInput = form.querySelector('input[name="month"]');
        if (monthInput) {
            monthInput.addEventListener('change', (e) => {
                const employeeId = form.querySelector('select[name="employee_id"]').value;
                if (employeeId) {
                    this.loadEmployeeData(employeeId);
                }
            });
        }

        // إضافة أزرار سريعة للحساب
        this.addQuickCalculationButtons(form);
    }

    addQuickCalculationButtons(form) {
        // البحث عن حاوي الأزرار أو إنشاؤه
        let buttonContainer = form.querySelector('.quick-calc-buttons');
        if (!buttonContainer) {
            buttonContainer = document.createElement('div');
            buttonContainer.className = 'quick-calc-buttons mt-3 mb-3';
            buttonContainer.innerHTML = `
                <div class="row">
                    <div class="col-md-12">
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-info" id="autoCalcInsurance">
                                <i class="fas fa-shield-alt me-1"></i>حساب التأمينات تلقائياً
                            </button>
                            <button type="button" class="btn btn-outline-warning" id="autoCalcTax">
                                <i class="fas fa-calculator me-1"></i>حساب الضرائب تلقائياً
                            </button>
                            <button type="button" class="btn btn-outline-success" id="resetCalculations">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين الحسابات
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // إدراج الحاوي قبل أزرار الحفظ
            const saveButtonContainer = form.querySelector('.modal-footer') || form.querySelector('.form-actions');
            if (saveButtonContainer) {
                saveButtonContainer.parentNode.insertBefore(buttonContainer, saveButtonContainer);
            }
        }

        // ربط أحداث الأزرار السريعة
        const autoCalcInsuranceBtn = buttonContainer.querySelector('#autoCalcInsurance');
        const autoCalcTaxBtn = buttonContainer.querySelector('#autoCalcTax');
        const resetCalculationsBtn = buttonContainer.querySelector('#resetCalculations');

        if (autoCalcInsuranceBtn) {
            autoCalcInsuranceBtn.addEventListener('click', () => {
                this.autoCalculateInsurance();
            });
        }

        if (autoCalcTaxBtn) {
            autoCalcTaxBtn.addEventListener('click', () => {
                this.autoCalculateTax();
            });
        }

        if (resetCalculationsBtn) {
            resetCalculationsBtn.addEventListener('click', () => {
                this.resetCalculations();
            });
        }
    }

    autoCalculateInsurance() {
        const form = document.getElementById('payrollForm');
        const basicSalary = parseFloat(form.querySelector('input[name="basic_salary"]').value) || 0;
        const socialInsuranceField = form.querySelector('input[name="social_insurance"]');

        if (socialInsuranceField && basicSalary > 0) {
            const socialInsurance = basicSalary * 0.09; // 9%
            socialInsuranceField.value = socialInsurance.toFixed(2);
            this.calculatePayroll();
            window.samApp.showAlert('تم حساب التأمينات الاجتماعية تلقائياً', 'success');
        }
    }

    autoCalculateTax() {
        const form = document.getElementById('payrollForm');
        const basicSalary = parseFloat(form.querySelector('input[name="basic_salary"]').value) || 0;
        const incomeTaxField = form.querySelector('input[name="income_tax"]');

        if (incomeTaxField && basicSalary > 0) {
            const incomeTax = this.calculateIncomeTax(basicSalary);
            incomeTaxField.value = incomeTax.toFixed(2);
            this.calculatePayroll();
            window.samApp.showAlert('تم حساب ضريبة الدخل تلقائياً', 'success');
        }
    }

    resetCalculations() {
        const form = document.getElementById('payrollForm');
        const calculatedFields = [
            'gross_salary', 'total_deductions', 'net_salary',
            'social_insurance', 'income_tax'
        ];

        calculatedFields.forEach(fieldName => {
            const field = form.querySelector(`input[name="${fieldName}"]`);
            if (field) {
                field.value = '0.00';
            }
        });

        this.calculatePayroll();
        window.samApp.showAlert('تم إعادة تعيين الحسابات', 'info');
    }

    resetPayrollForm() {
        const form = document.getElementById('payrollForm');
        const fieldsToReset = [
            'basic_salary', 'working_days', 'attendance_days',
            'housing_allowance', 'transport_allowance', 'food_allowance', 'other_allowances',
            'performance_bonus', 'overtime_hours', 'other_bonuses',
            'social_insurance', 'income_tax', 'advances',
            'lateness_deduction', 'absence_deduction', 'other_deductions',
            'gross_salary', 'total_deductions', 'net_salary'
        ];

        fieldsToReset.forEach(fieldName => {
            const field = form.querySelector(`input[name="${fieldName}"]`);
            if (field) {
                field.value = '';
            }
        });
    }

    loadPayrollData() {
        // إظهار مؤشر التحميل
        const tableBody = document.getElementById('payrollTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `;
        }

        // تحميل البيانات
        setTimeout(() => {
            this.loadEmployeeOptions();
            this.loadPayrollTable();
            this.updatePayrollStats();
        }, 100);
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const selects = document.querySelectorAll('#employeeFilter, select[name="employee_id"]');

        selects.forEach(select => {
            const isFilter = select.id === 'employeeFilter';
            select.innerHTML = isFilter ? '<option value="">جميع الموظفين</option>' : '<option value="">اختر الموظف</option>';

            employees.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
            });
        });
    }

    loadPayrollTable() {
        let payrolls = Database.getAll('payroll') || [];
        const employees = Database.getEmployees() || [];

        // إذا لم تكن هناك كشوف رواتب، أظهر جميع الموظفين النشطين
        if (payrolls.length === 0 && this.currentMonth) {
            const activeEmployees = employees.filter(emp => emp.status === 'active');
            payrolls = activeEmployees.map(emp => ({
                id: `temp_${emp.id}`,
                employee_id: emp.id,
                month: this.currentMonth,
                basic_salary: emp.salary || 0,
                housing_allowance: 0,
                transport_allowance: 0,
                food_allowance: 0,
                other_allowances: 0,
                performance_bonus: 0,
                overtime_hours: 0,
                other_bonuses: 0,
                social_insurance: 0,
                income_tax: 0,
                advances: 0,
                lateness_deduction: 0,
                absence_deduction: 0,
                other_deductions: 0,
                gross_salary: emp.salary || 0,
                total_deductions: 0,
                net_salary: emp.salary || 0,
                status: 'pending',
                isTemporary: true
            }));
        }

        // Apply filters
        if (this.currentMonth) {
            payrolls = payrolls.filter(p => p.month === this.currentMonth);
        }

        if (this.selectedEmployee) {
            payrolls = payrolls.filter(p => p.employee_id === this.selectedEmployee);
        }

        if (this.selectedStatus) {
            payrolls = payrolls.filter(p => p.status === this.selectedStatus);
        }

        // Sort by creation date (newest first)
        payrolls.sort((a, b) => {
            const dateA = new Date(a.created_at || a.month + '-01');
            const dateB = new Date(b.created_at || b.month + '-01');
            return dateB - dateA;
        });

        this.renderPayrollTable(payrolls);
    }

    renderPayrollTable(payrolls) {
        const tbody = document.getElementById('payrollTableBody');
        const employees = Database.getEmployees();

        if (payrolls.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-2">لا توجد كشوف رواتب</h5>
                            <p class="text-muted mb-3">لم يتم العثور على كشوف رواتب للشهر المحدد</p>
                            <button class="btn btn-primary" onclick="document.getElementById('generatePayrollBtn').click()">
                                <i class="fas fa-plus me-2"></i>إنشاء كشوف رواتب
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            const statusBadge = this.getStatusBadge(payroll.status);

            // حساب القيم بشكل صحيح
            const allowances = this.calculateAllowances(payroll);
            const bonuses = this.calculateBonuses(payroll);
            const grossSalary = (payroll.basic_salary || 0) + allowances + bonuses;
            const netSalary = Math.max(0, grossSalary - (payroll.total_deductions || 0));

            return `
                <tr ${payroll.isTemporary ? 'class="table-warning"' : ''}>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee?.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee?.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                                <small class="text-muted">${employee?.employee_number || ''}</small>
                                ${payroll.isTemporary ? '<small class="text-warning"><i class="fas fa-exclamation-triangle"></i> لم يتم إنشاء كشف راتب</small>' : ''}
                            </div>
                        </div>
                    </td>
                    <td>${this.formatMonth(payroll.month)}</td>
                    <td class="text-end">${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                    <td class="text-end text-success">${window.samApp.formatCurrency(allowances)}</td>
                    <td class="text-end text-info">${window.samApp.formatCurrency(bonuses)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                    <td class="text-end"><strong class="text-primary">${window.samApp.formatCurrency(netSalary)}</strong></td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${payroll.isTemporary ? `
                                <button class="btn btn-outline-primary create-payroll"
                                        data-employee-id="${payroll.employee_id}"
                                        data-month="${payroll.month}" title="إنشاء كشف راتب">
                                    <i class="fas fa-plus"></i>
                                </button>
                            ` : `
                                <button class="btn btn-outline-warning auto-update-payroll"
                                        data-employee-id="${payroll.employee_id}" title="تحديث تلقائي">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-warning auto-update-payroll"
                                        data-employee-id="${payroll.employee_id}" title="تحديث تلقائي">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-primary view-payroll"
                                        data-payroll-id="${payroll.id}" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-success edit-payroll"
                                        data-payroll-id="${payroll.id}" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info print-payroll"
                                        data-payroll-id="${payroll.id}" title="طباعة قسيمة الراتب">
                                    <i class="fas fa-print"></i>
                                </button>
                                ${payroll.status === 'pending' ? `
                                <button class="btn btn-outline-warning mark-paid"
                                        data-payroll-id="${payroll.id}" title="تحديد كمدفوع">
                                    <i class="fas fa-check"></i>
                                </button>
                                ` : ''}
                                <button class="btn btn-outline-danger delete-payroll"
                                        data-payroll-id="${payroll.id}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            `}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        this.bindTableEvents();
    }

    bindTableEvents() {
        // View payroll
        document.querySelectorAll('.view-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.view-payroll').dataset.payrollId;
                this.viewPayroll(payrollId);
            });
        });

        // Edit payroll
        document.querySelectorAll('.edit-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.edit-payroll').dataset.payrollId;
                const payroll = Database.getById('payroll', payrollId);
                this.showPayrollModal(payroll);
            });
        });

        // Print payroll
        document.querySelectorAll('.print-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.print-payroll').dataset.payrollId;
                this.printPayslip(payrollId);
            });
        });

        // Mark as paid
        document.querySelectorAll('.mark-paid').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.mark-paid').dataset.payrollId;
                this.markAsPaid(payrollId);
            });
        });

        // Create payroll for employee
        document.querySelectorAll('.create-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.create-payroll').dataset.employeeId;
                const month = e.target.closest('.create-payroll').dataset.month;
                this.createPayrollForEmployee(employeeId, month);
            });
        });

        // Delete payroll
        document.querySelectorAll('.delete-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const payrollId = e.target.closest('.delete-payroll').dataset.payrollId;
                this.deletePayroll(payrollId);
            });
        });

        // Auto update single payroll
        document.querySelectorAll('.auto-update-payroll').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const employeeId = e.target.closest('.auto-update-payroll').dataset.employeeId;
                this.handleAutoUpdateSingle(employeeId);
            });
        });
    }

    calculateAllowances(payroll) {
        if (!payroll) return 0;

        const housing = parseFloat(payroll.housing_allowance) || 0;
        const transport = parseFloat(payroll.transport_allowance) || 0;
        const food = parseFloat(payroll.food_allowance) || 0;
        const other = parseFloat(payroll.other_allowances) || 0;

        return Math.round((housing + transport + food + other) * 100) / 100;
    }

    calculateBonuses(payroll) {
        if (!payroll) return 0;

        const performanceBonus = parseFloat(payroll.performance_bonus) || 0;
        const overtimeHours = parseFloat(payroll.overtime_hours) || 0;
        const otherBonuses = parseFloat(payroll.other_bonuses) || 0;

        // حساب قيمة الساعات الإضافية
        const basicSalary = parseFloat(payroll.basic_salary) || 0;
        const overtimePay = overtimeHours * this.getOvertimeRate(basicSalary);

        return Math.round((performanceBonus + overtimePay + otherBonuses) * 100) / 100;
    }

    getOvertimeRate(basicSalary) {
        if (!basicSalary || basicSalary <= 0) return 0;

        const settings = Database.getSettings();
        const workingDaysPerMonth = 22; // أيام العمل الفعلية في الشهر
        const hoursPerDay = 8; // ساعات العمل اليومية

        // حساب الأجر بالساعة
        const hourlyRate = basicSalary / (workingDaysPerMonth * hoursPerDay);

        // معدل الإضافي (عادة 1.5 من الأجر العادي)
        const overtimeMultiplier = settings.attendance?.overtime_rate || 1.5;

        return Math.round((hourlyRate * overtimeMultiplier) * 100) / 100;
    }

    calculateOvertimePay(payroll) {
        if (!payroll) return 0;

        const overtimeHours = parseFloat(payroll.overtime_hours) || 0;
        if (overtimeHours <= 0) return 0;

        const basicSalary = parseFloat(payroll.basic_salary) || 0;
        const overtimeRate = this.getOvertimeRate(basicSalary);

        return Math.round((overtimeHours * overtimeRate) * 100) / 100;
    }

    getStatusBadge(status) {
        const statusMap = {
            'pending': {
                class: 'warning',
                text: 'في الانتظار',
                icon: 'fas fa-hourglass-half'
            },
            'paid': {
                class: 'success',
                text: 'مدفوعة',
                icon: 'fas fa-check-circle'
            },
            'cancelled': {
                class: 'danger',
                text: 'ملغية',
                icon: 'fas fa-times-circle'
            }
        };

        const statusInfo = statusMap[status] || {
            class: 'secondary',
            text: 'غير محدد',
            icon: 'fas fa-question-circle'
        };

        return `<span class="badge bg-${statusInfo.class} status-badge">
                    <i class="${statusInfo.icon} me-1"></i>
                    ${statusInfo.text}
                </span>`;
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }

    updatePayrollStats() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');

        // حساب الإجمالي بشكل صحيح
        let totalPayroll = 0;
        let paidCount = 0;
        let pendingCount = 0;

        payrolls.forEach(p => {
            // إعادة حساب صافي الراتب للتأكد من الدقة
            const allowances = this.calculateAllowances(p);
            const bonuses = this.calculateBonuses(p);
            const grossSalary = (p.basic_salary || 0) + allowances + bonuses;
            const netSalary = Math.max(0, grossSalary - (p.total_deductions || 0));

            totalPayroll += netSalary;

            if (p.status === 'paid') paidCount++;
            else if (p.status === 'pending') pendingCount++;
        });

        // عدد الموظفين النشطين
        const employeeCount = employees.length;

        // عدد الموظفين الذين لديهم كشوف رواتب
        const payrollEmployeeCount = payrolls.length;

        document.getElementById('totalPayroll').textContent = window.samApp.formatCurrency(totalPayroll);
        document.getElementById('paidCount').textContent = paidCount;
        document.getElementById('pendingCount').textContent = pendingCount;
        document.getElementById('employeeCount').textContent = `${payrollEmployeeCount}/${employeeCount}`;
    }

    showPayrollModal(payroll = null) {
        const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
        const form = document.getElementById('payrollForm');
        const title = document.getElementById('payrollModalTitle');

        if (payroll) {
            title.textContent = 'تعديل راتب';
            this.populatePayrollForm(form, payroll);
            this.selectedPayroll = payroll;
        } else {
            title.textContent = 'إضافة راتب';
            form.reset();
            form.querySelector('input[name="month"]').value = this.currentMonth;
            this.selectedPayroll = null;
        }

        modal.show();
    }

    populatePayrollForm(form, payroll) {
        Object.keys(payroll).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = payroll[key] || '';
            }
        });

        this.calculatePayroll();
    }

    loadEmployeeData(employeeId) {
        if (!employeeId) return;

        const employee = Database.getEmployee(employeeId);
        if (employee) {
            const form = document.getElementById('payrollForm');
            const month = form.querySelector('input[name="month"]').value;

            form.querySelector('input[name="basic_salary"]').value = employee.salary || 0;

            // Load attendance data for the month
            const attendance = Database.getAttendance({
                employee_id: employeeId,
                month: month
            });

            const workingDays = this.getWorkingDaysInMonth(month, employee);
            const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, month, employee);
            const attendanceDays = attendanceStats.presentDays;

            form.querySelector('input[name="working_days"]').value = workingDays;
            form.querySelector('input[name="attendance_days"]').value = attendanceDays;

            // Load employee-specific deductions
            this.loadEmployeeDeductions(employeeId, month, form);

            this.calculatePayroll();
        }
    }

    loadEmployeeDeductions(employeeId, month, form) {
        // استخدام حاسبة الساعات الإضافية والخصومات
        if (!window.overtimeCalculator) {
            console.warn('OvertimeCalculator not available');
            return;
        }

        const calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employeeId, month);

        if (calculations) {
            // تحديث الإضافات بحذر
            const overtimeField = form.querySelector('input[name="overtime_hours"]');
            const transportField = form.querySelector('input[name="transport_allowance"]');
            const housingField = form.querySelector('input[name="housing_allowance"]');
            const otherAllowancesField = form.querySelector('input[name="other_allowances"]');

            if (overtimeField && calculations.details.overtime.hours > 0) {
                overtimeField.value = calculations.details.overtime.hours.toFixed(2);
            }
            if (transportField) {
                transportField.value = calculations.allowances.transport.toFixed(2);
            }
            if (housingField) {
                housingField.value = calculations.allowances.housing.toFixed(2);
            }
            if (otherAllowancesField) {
                otherAllowancesField.value = calculations.allowances.other.toFixed(2);
            }

            // تحديث الخصومات بحذر
            const advancesField = form.querySelector('input[name="advances"]');
            const latenessField = form.querySelector('input[name="lateness_deduction"]');
            const absenceField = form.querySelector('input[name="absence_deduction"]');
            const otherDeductionsField = form.querySelector('input[name="other_deductions"]');

            if (advancesField) {
                advancesField.value = calculations.deductions.advances.toFixed(2);
            }
            if (latenessField) {
                latenessField.value = calculations.deductions.lateness.toFixed(2);
            }
            if (absenceField) {
                absenceField.value = calculations.deductions.absence.toFixed(2);
            }
            if (otherDeductionsField) {
                otherDeductionsField.value = calculations.deductions.penalties.toFixed(2);
            }

            // حساب التأمينات الاجتماعية وضريبة الدخل
            const employee = Database.getEmployee(employeeId);
            if (employee) {
                const basicSalary = employee.salary || 0;
                const socialInsuranceField = form.querySelector('input[name="social_insurance"]');
                const incomeTaxField = form.querySelector('input[name="income_tax"]');

                if (socialInsuranceField) {
                    const socialInsurance = basicSalary * 0.09; // 9%
                    socialInsuranceField.value = socialInsurance.toFixed(2);
                }

                if (incomeTaxField) {
                    const incomeTax = this.calculateIncomeTax(basicSalary);
                    incomeTaxField.value = incomeTax.toFixed(2);
                }
            }

            // حفظ التفاصيل للعرض في مفردات الراتب
            this.payrollDetails = calculations.details;
        }
    }

    getWorkingDaysInMonth(monthStr, employee = null) {
        const [year, month] = monthStr.split('-');
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();
        let workingDays = 0;

        // Use employee-specific working days if available, otherwise use global settings
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            if (workingDaysList.includes(dayName)) {
                workingDays++;
            }
        }

        return workingDays;
    }

    calculateAttendanceStats(attendance, workingDays, month, employee) {
        // Create a map of all working days in the month
        const [year, monthNum] = month.split('-');
        const workingDaysMap = this.getWorkingDaysMap(year, monthNum, employee);

        let presentDays = 0;
        let absentDays = 0;
        let lateCount = 0;
        let earlyLeaveCount = 0;

        // Process each working day
        workingDaysMap.forEach((isWorkingDay, dateStr) => {
            if (!isWorkingDay) return; // Skip non-working days

            // Find attendance record for this date
            const record = attendance.find(a => a.date === dateStr);

            if (record) {
                // Check the status of the record
                switch (record.status) {
                    case 'present':
                    case 'late':
                    case 'early_leave':
                        presentDays++;
                        if (record.status === 'late') lateCount++;
                        if (record.status === 'early_leave') earlyLeaveCount++;
                        break;
                    case 'absent':
                    case 'unauthorized_absence':
                        absentDays++;
                        break;
                    case 'vacation':
                    case 'sick_leave':
                    case 'authorized_leave':
                        // Count as present for salary calculation purposes
                        presentDays++;
                        break;
                    default:
                        // If check_in exists, consider as present
                        if (record.check_in) {
                            presentDays++;
                        } else {
                            absentDays++;
                        }
                        break;
                }
            } else {
                // No record found for this working day = absent
                absentDays++;
            }
        });

        return {
            presentDays,
            absentDays,
            lateCount,
            earlyLeaveCount,
            totalWorkingDays: workingDays,
            attendanceRate: workingDays > 0 ? Math.round((presentDays / workingDays) * 100) : 0
        };
    }

    getWorkingDaysMap(year, month, employee = null) {
        const workingDaysMap = new Map();
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();

        // Get working days configuration
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        // Map each day of the month
        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            const isWorkingDay = workingDaysList.includes(dayName);

            workingDaysMap.set(dateStr, isWorkingDay);
        }

        return workingDaysMap;
    }

    // نظام التحديث التلقائي للرواتب بناءً على الحضور
    calculatePayrollFromAttendance(employeeId, month) {
        try {
            console.log(`حساب الراتب التلقائي للموظف ${employeeId} للشهر ${month}`);

            const employee = Database.getEmployee(employeeId);
            if (!employee) {
                throw new Error('الموظف غير موجود');
            }

            const attendance = Database.getAttendance({ employee_id: employeeId, month });
            const workingDays = this.getWorkingDaysInMonth(month, employee);
            const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, month, employee);

            // الراتب الأساسي النسبي
            const basicSalary = parseFloat(employee.salary) || 0;
            const proportionalSalary = workingDays > 0 ?
                (basicSalary * attendanceStats.presentDays / workingDays) :
                basicSalary;

            // حساب البدلات
            const allowances = this.calculateAllowancesFromAttendance(employee, attendanceStats, month);

            // حساب الحوافز والمكافآت
            const bonuses = this.calculateBonusesFromAttendance(employee, attendance, attendanceStats, month);

            // حساب الخصومات والاستقطاعات
            const deductions = this.calculateDeductionsFromAttendance(employee, attendance, attendanceStats, month, proportionalSalary);

            // حساب الإجماليات
            const grossSalary = proportionalSalary + allowances.total + bonuses.total;
            const netSalary = Math.max(0, grossSalary - deductions.total);

            return {
                employee_id: employeeId,
                month: month,
                basic_salary: basicSalary,
                proportional_salary: proportionalSalary,
                working_days: workingDays,
                attendance_days: attendanceStats.presentDays,

                // البدلات
                housing_allowance: allowances.housing,
                transport_allowance: allowances.transport,
                food_allowance: allowances.food,
                other_allowances: allowances.other,

                // الحوافز والمكافآت
                performance_bonus: bonuses.performance,
                overtime_hours: bonuses.overtimeHours,
                overtime_pay: bonuses.overtime,
                punctuality_bonus: bonuses.punctuality,
                attendance_bonus: bonuses.attendance,
                other_bonuses: bonuses.other,

                // الخصومات والاستقطاعات
                social_insurance: deductions.socialInsurance,
                income_tax: deductions.incomeTax,
                advances: deductions.advances,
                lateness_deduction: deductions.lateness,
                absence_deduction: deductions.absence,
                early_leave_deduction: deductions.earlyLeave,
                other_deductions: deductions.other,

                // الإجماليات
                gross_salary: grossSalary,
                total_deductions: deductions.total,
                net_salary: netSalary,

                // إحصائيات الحضور
                attendance_stats: attendanceStats,

                // تفاصيل إضافية
                calculation_details: {
                    allowances_breakdown: allowances,
                    bonuses_breakdown: bonuses,
                    deductions_breakdown: deductions
                },

                calculated_at: new Date().toISOString(),
                status: 'pending'
            };

        } catch (error) {
            console.error('خطأ في حساب الراتب التلقائي:', error);
            throw error;
        }
    }

    calculateAllowancesFromAttendance(employee, attendanceStats, month) {
        const settings = Database.getSettings();
        const basicSalary = parseFloat(employee.salary) || 0;

        // بدل السكن (ثابت أو نسبة من الراتب)
        let housingAllowance = 0;
        if (employee.housing_allowance_type === 'fixed') {
            housingAllowance = parseFloat(employee.housing_allowance) || 0;
        } else if (employee.housing_allowance_type === 'percentage') {
            const percentage = parseFloat(employee.housing_allowance_percentage) || 25; // 25% افتراضي
            housingAllowance = basicSalary * (percentage / 100);
        }

        // بدل المواصلات (يعتمد على أيام الحضور الفعلية)
        let transportAllowance = 0;
        const dailyTransportRate = parseFloat(employee.daily_transport_allowance) || 0;
        if (dailyTransportRate > 0) {
            transportAllowance = dailyTransportRate * attendanceStats.presentDays;
        } else {
            // بدل مواصلات ثابت شهري
            transportAllowance = parseFloat(employee.transport_allowance) || 0;
        }

        // بدل الطعام (يعتمد على أيام الحضور الفعلية)
        let foodAllowance = 0;
        const dailyFoodRate = parseFloat(employee.daily_food_allowance) || 0;
        if (dailyFoodRate > 0) {
            foodAllowance = dailyFoodRate * attendanceStats.presentDays;
        } else {
            // بدل طعام ثابت شهري
            foodAllowance = parseFloat(employee.food_allowance) || 0;
        }

        // بدلات أخرى
        const otherAllowances = parseFloat(employee.other_allowances) || 0;

        const total = housingAllowance + transportAllowance + foodAllowance + otherAllowances;

        return {
            housing: Math.round(housingAllowance * 100) / 100,
            transport: Math.round(transportAllowance * 100) / 100,
            food: Math.round(foodAllowance * 100) / 100,
            other: Math.round(otherAllowances * 100) / 100,
            total: Math.round(total * 100) / 100
        };
    }

    calculateBonusesFromAttendance(employee, attendance, attendanceStats, month) {
        const settings = Database.getSettings();
        const basicSalary = parseFloat(employee.salary) || 0;

        // حافز الأداء (ثابت شهري أو مشروط بالحضور)
        let performanceBonus = 0;
        const monthlyPerformanceBonus = parseFloat(employee.performance_bonus) || 0;
        if (monthlyPerformanceBonus > 0) {
            // إذا كان معدل الحضور أكثر من 95% يحصل على الحافز كاملاً
            if (attendanceStats.attendanceRate >= 95) {
                performanceBonus = monthlyPerformanceBonus;
            } else if (attendanceStats.attendanceRate >= 85) {
                // إذا كان معدل الحضور بين 85-95% يحصل على 50% من الحافز
                performanceBonus = monthlyPerformanceBonus * 0.5;
            }
        }

        // حساب الساعات الإضافية
        let overtimeHours = 0;
        let overtimePay = 0;

        attendance.forEach(record => {
            if (record.overtime_hours && record.overtime_hours > 0) {
                overtimeHours += parseFloat(record.overtime_hours);
            }
        });

        if (overtimeHours > 0) {
            const overtimeRate = this.getOvertimeRate(basicSalary);
            overtimePay = overtimeHours * overtimeRate;
        }

        // حافز الانضباط (عدم التأخير)
        let punctualityBonus = 0;
        const punctualityBonusRate = parseFloat(employee.punctuality_bonus) || 0;
        if (punctualityBonusRate > 0 && attendanceStats.lateCount === 0) {
            punctualityBonus = punctualityBonusRate;
        }

        // حافز الحضور المثالي
        let attendanceBonus = 0;
        const attendanceBonusRate = parseFloat(employee.attendance_bonus) || 0;
        if (attendanceBonusRate > 0 && attendanceStats.attendanceRate === 100) {
            attendanceBonus = attendanceBonusRate;
        }

        // حوافز أخرى
        const otherBonuses = parseFloat(employee.other_bonuses) || 0;

        const total = performanceBonus + overtimePay + punctualityBonus + attendanceBonus + otherBonuses;

        return {
            performance: Math.round(performanceBonus * 100) / 100,
            overtimeHours: Math.round(overtimeHours * 100) / 100,
            overtime: Math.round(overtimePay * 100) / 100,
            punctuality: Math.round(punctualityBonus * 100) / 100,
            attendance: Math.round(attendanceBonus * 100) / 100,
            other: Math.round(otherBonuses * 100) / 100,
            total: Math.round(total * 100) / 100
        };
    }

    calculateDeductionsFromAttendance(employee, attendance, attendanceStats, month, grossSalary) {
        const settings = Database.getSettings();

        // التأمينات الاجتماعية
        const socialInsuranceRate = settings.payroll?.social_insurance_rate || 0.09;
        const socialInsurance = grossSalary * socialInsuranceRate;

        // ضريبة الدخل
        const incomeTax = this.calculateIncomeTax(grossSalary);

        // السلف والقروض
        const advances = this.getEmployeeAdvances(employee.id, month);

        // خصم التأخير
        let latenessDeduction = 0;
        const lateDeductionRate = parseFloat(employee.late_deduction_rate) || 0;
        if (lateDeductionRate > 0 && attendanceStats.lateCount > 0) {
            // خصم عن كل دقيقة تأخير
            let totalLateMinutes = 0;
            attendance.forEach(record => {
                if (record.late_minutes && record.late_minutes > 0) {
                    totalLateMinutes += parseFloat(record.late_minutes);
                }
            });
            latenessDeduction = (totalLateMinutes / 60) * lateDeductionRate; // خصم بالساعة
        }

        // خصم الغياب
        let absenceDeduction = 0;
        if (attendanceStats.absentDays > 0) {
            const dailySalary = parseFloat(employee.salary) / attendanceStats.totalWorkingDays;
            absenceDeduction = dailySalary * attendanceStats.absentDays;
        }

        // خصم الانصراف المبكر
        let earlyLeaveDeduction = 0;
        const earlyLeaveDeductionRate = parseFloat(employee.early_leave_deduction_rate) || 0;
        if (earlyLeaveDeductionRate > 0 && attendanceStats.earlyLeaveCount > 0) {
            let totalEarlyMinutes = 0;
            attendance.forEach(record => {
                if (record.early_leave_minutes && record.early_leave_minutes > 0) {
                    totalEarlyMinutes += parseFloat(record.early_leave_minutes);
                }
            });
            earlyLeaveDeduction = (totalEarlyMinutes / 60) * earlyLeaveDeductionRate;
        }

        // خصومات أخرى (جزاءات، مخالفات، إلخ)
        const otherDeductions = this.getEmployeePenalties(employee.id, month);

        const total = socialInsurance + incomeTax + advances + latenessDeduction +
                     absenceDeduction + earlyLeaveDeduction + otherDeductions;

        return {
            socialInsurance: Math.round(socialInsurance * 100) / 100,
            incomeTax: Math.round(incomeTax * 100) / 100,
            advances: Math.round(advances * 100) / 100,
            lateness: Math.round(latenessDeduction * 100) / 100,
            absence: Math.round(absenceDeduction * 100) / 100,
            earlyLeave: Math.round(earlyLeaveDeduction * 100) / 100,
            other: Math.round(otherDeductions * 100) / 100,
            total: Math.round(total * 100) / 100
        };
    }

    calculateIncomeTax(grossSalary) {
        const settings = Database.getSettings();
        const taxThreshold = settings.payroll?.tax_threshold || 3000;

        if (grossSalary <= taxThreshold) {
            return 0;
        }

        const taxableSalary = grossSalary - taxThreshold;
        const taxBrackets = settings.payroll?.income_tax_brackets || [
            { min: 0, max: 3000, rate: 0 },
            { min: 3000, max: 5000, rate: 0.05 },
            { min: 5000, max: 10000, rate: 0.10 },
            { min: 10000, max: Infinity, rate: 0.15 }
        ];

        let tax = 0;
        let remainingSalary = taxableSalary;

        for (const bracket of taxBrackets) {
            if (remainingSalary <= 0) break;

            const bracketRange = bracket.max - bracket.min;
            const taxableInBracket = Math.min(remainingSalary, bracketRange);
            tax += taxableInBracket * bracket.rate;
            remainingSalary -= taxableInBracket;
        }

        return Math.round(tax * 100) / 100;
    }

    getEmployeeAdvances(employeeId, month) {
        try {
            const advances = Database.getAll('advances') || [];
            const monthAdvances = advances.filter(advance =>
                advance.employee_id === employeeId &&
                advance.deduction_month === month &&
                advance.status === 'approved'
            );

            return monthAdvances.reduce((total, advance) => {
                return total + (parseFloat(advance.monthly_deduction) || 0);
            }, 0);
        } catch (error) {
            console.warn('خطأ في جلب السلف:', error);
            return 0;
        }
    }

    getEmployeePenalties(employeeId, month) {
        try {
            const penalties = Database.getAll('penalties') || [];
            const monthPenalties = penalties.filter(penalty =>
                penalty.employee_id === employeeId &&
                penalty.month === month &&
                penalty.status === 'approved'
            );

            return monthPenalties.reduce((total, penalty) => {
                return total + (parseFloat(penalty.amount) || 0);
            }, 0);
        } catch (error) {
            console.warn('خطأ في جلب الجزاءات:', error);
            return 0;
        }
    }

    // تحديث تلقائي لجميع رواتب الموظفين للشهر المحدد
    autoUpdateAllPayrolls(month = null) {
        const targetMonth = month || this.currentMonth;

        return new Promise((resolve, reject) => {
            try {
                console.log(`بدء التحديث التلقائي لجميع الرواتب للشهر ${targetMonth}`);

                const employees = Database.getEmployees().filter(emp => emp.status === 'active');
                const results = {
                    updated: 0,
                    created: 0,
                    errors: 0,
                    details: []
                };

                employees.forEach(employee => {
                    try {
                        // حساب الراتب التلقائي
                        const calculatedPayroll = this.calculatePayrollFromAttendance(employee.id, targetMonth);

                        // البحث عن راتب موجود
                        const existingPayrolls = Database.getAll('payroll') || [];
                        const existingPayroll = existingPayrolls.find(p =>
                            p.employee_id === employee.id && p.month === targetMonth
                        );

                        if (existingPayroll) {
                            // تحديث الراتب الموجود
                            const updatedPayroll = {
                                ...existingPayroll,
                                ...calculatedPayroll,
                                id: existingPayroll.id,
                                updated_at: new Date().toISOString()
                            };

                            Database.update('payroll', existingPayroll.id, updatedPayroll);
                            results.updated++;
                            results.details.push({
                                employee: employee.name,
                                action: 'updated',
                                netSalary: calculatedPayroll.net_salary
                            });
                        } else {
                            // إنشاء راتب جديد
                            const newPayroll = {
                                ...calculatedPayroll,
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString()
                            };

                            Database.create('payroll', newPayroll);
                            results.created++;
                            results.details.push({
                                employee: employee.name,
                                action: 'created',
                                netSalary: calculatedPayroll.net_salary
                            });
                        }

                    } catch (employeeError) {
                        console.error(`خطأ في تحديث راتب ${employee.name}:`, employeeError);
                        results.errors++;
                        results.details.push({
                            employee: employee.name,
                            action: 'error',
                            error: employeeError.message
                        });
                    }
                });

                console.log('نتائج التحديث التلقائي:', results);
                resolve(results);

            } catch (error) {
                console.error('خطأ في التحديث التلقائي:', error);
                reject(error);
            }
        });
    }

    // تحديث راتب موظف واحد تلقائياً
    autoUpdateSinglePayroll(employeeId, month = null) {
        const targetMonth = month || this.currentMonth;

        try {
            console.log(`تحديث تلقائي لراتب الموظف ${employeeId} للشهر ${targetMonth}`);

            const calculatedPayroll = this.calculatePayrollFromAttendance(employeeId, targetMonth);

            const existingPayrolls = Database.getAll('payroll') || [];
            const existingPayroll = existingPayrolls.find(p =>
                p.employee_id === employeeId && p.month === targetMonth
            );

            if (existingPayroll) {
                const updatedPayroll = {
                    ...existingPayroll,
                    ...calculatedPayroll,
                    id: existingPayroll.id,
                    updated_at: new Date().toISOString()
                };

                Database.update('payroll', existingPayroll.id, updatedPayroll);
                return { action: 'updated', payroll: updatedPayroll };
            } else {
                const newPayroll = {
                    ...calculatedPayroll,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                const created = Database.create('payroll', newPayroll);
                return { action: 'created', payroll: created };
            }

        } catch (error) {
            console.error('خطأ في التحديث التلقائي للموظف:', error);
            throw error;
        }
    }

    handleAutoUpdateAll() {
        // عرض نافذة تأكيد
        const confirmModal = `
            <div class="modal fade" id="autoUpdateConfirmModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تأكيد التحديث التلقائي
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>تحذير:</strong> سيتم تحديث جميع رواتب الموظفين للشهر الحالي تلقائياً بناءً على سجل الحضور والانصراف.</p>
                            <p>هذا سيؤثر على:</p>
                            <ul>
                                <li>البدلات (حسب أيام الحضور الفعلية)</li>
                                <li>الحوافز والمكافآت (حسب الأداء والانضباط)</li>
                                <li>الخصومات والاستقطاعات (حسب التأخير والغياب)</li>
                                <li>صافي الراتب النهائي</li>
                            </ul>
                            <p class="text-danger"><strong>ملاحظة:</strong> سيتم استبدال أي تعديلات يدوية سابقة.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-warning" id="confirmAutoUpdateBtn">
                                <i class="fas fa-sync-alt me-2"></i>
                                تأكيد التحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة إلى الصفحة
        document.body.insertAdjacentHTML('beforeend', confirmModal);

        const modal = new bootstrap.Modal(document.getElementById('autoUpdateConfirmModal'));
        modal.show();

        // ربط حدث التأكيد
        document.getElementById('confirmAutoUpdateBtn').addEventListener('click', () => {
            modal.hide();
            this.executeAutoUpdateAll();
        });

        // حذف النافذة بعد الإغلاق
        document.getElementById('autoUpdateConfirmModal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('autoUpdateConfirmModal').remove();
        });
    }

    executeAutoUpdateAll() {
        // عرض مؤشر التحميل
        const loadingAlert = window.samApp.showAlert(
            '<div class="d-flex align-items-center"><div class="spinner-border spinner-border-sm me-2"></div>جاري التحديث التلقائي لجميع الرواتب...</div>',
            'info',
            0 // لا تخفي تلقائياً
        );

        // تنفيذ التحديث التلقائي
        this.autoUpdateAllPayrolls(this.currentMonth)
            .then(results => {
                // إخفاء مؤشر التحميل
                if (loadingAlert && loadingAlert.close) {
                    loadingAlert.close();
                }

                // عرض النتائج
                this.showAutoUpdateResults(results);

                // تحديث الجدول
                this.loadPayrollData();
                this.updatePayrollStats();
            })
            .catch(error => {
                // إخفاء مؤشر التحميل
                if (loadingAlert && loadingAlert.close) {
                    loadingAlert.close();
                }

                console.error('خطأ في التحديث التلقائي:', error);
                window.samApp.showAlert(
                    'حدث خطأ في التحديث التلقائي: ' + error.message,
                    'danger'
                );
            });
    }

    showAutoUpdateResults(results) {
        const resultModal = `
            <div class="modal fade" id="autoUpdateResultsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-check-circle me-2"></i>
                                نتائج التحديث التلقائي
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="card bg-primary text-white text-center">
                                        <div class="card-body">
                                            <h3>${results.created}</h3>
                                            <p class="mb-0">رواتب جديدة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-info text-white text-center">
                                        <div class="card-body">
                                            <h3>${results.updated}</h3>
                                            <p class="mb-0">رواتب محدثة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-danger text-white text-center">
                                        <div class="card-body">
                                            <h3>${results.errors}</h3>
                                            <p class="mb-0">أخطاء</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6>تفاصيل التحديث:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>الإجراء</th>
                                            <th>صافي الراتب</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${results.details.map(detail => `
                                            <tr>
                                                <td>${detail.employee}</td>
                                                <td>
                                                    <span class="badge bg-${detail.action === 'created' ? 'primary' : detail.action === 'updated' ? 'info' : 'danger'}">
                                                        ${detail.action === 'created' ? 'جديد' : detail.action === 'updated' ? 'محدث' : 'خطأ'}
                                                    </span>
                                                </td>
                                                <td>
                                                    ${detail.netSalary ? window.samApp.formatCurrency(detail.netSalary) : detail.error || '-'}
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', resultModal);

        const modal = new bootstrap.Modal(document.getElementById('autoUpdateResultsModal'));
        modal.show();

        // حذف النافذة بعد الإغلاق
        document.getElementById('autoUpdateResultsModal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('autoUpdateResultsModal').remove();
        });
    }

    handleAutoUpdateSingle(employeeId) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }

        // عرض تأكيد
        if (!confirm(`هل تريد تحديث راتب ${employee.name} تلقائياً للشهر ${this.formatMonth(this.currentMonth)}؟\n\nسيتم حساب الراتب بناءً على سجل الحضور والانصراف.`)) {
            return;
        }

        // عرض مؤشر التحميل
        const loadingAlert = window.samApp.showAlert(
            `<div class="d-flex align-items-center"><div class="spinner-border spinner-border-sm me-2"></div>جاري تحديث راتب ${employee.name}...</div>`,
            'info',
            0
        );

        try {
            const result = this.autoUpdateSinglePayroll(employeeId, this.currentMonth);

            // إخفاء مؤشر التحميل
            if (loadingAlert && loadingAlert.close) {
                loadingAlert.close();
            }

            // عرض النتيجة
            const actionText = result.action === 'created' ? 'تم إنشاء' : 'تم تحديث';
            window.samApp.showAlert(
                `${actionText} راتب ${employee.name} بنجاح<br>صافي الراتب: ${window.samApp.formatCurrency(result.payroll.net_salary)}`,
                'success'
            );

            // تحديث الجدول
            this.loadPayrollData();
            this.updatePayrollStats();

        } catch (error) {
            // إخفاء مؤشر التحميل
            if (loadingAlert && loadingAlert.close) {
                loadingAlert.close();
            }

            console.error('خطأ في التحديث التلقائي:', error);
            window.samApp.showAlert(
                `حدث خطأ في تحديث راتب ${employee.name}: ${error.message}`,
                'danger'
            );
        }
    }

    setupAttendanceListener() {
        // Listen for attendance updates
        document.addEventListener('attendanceUpdated', (event) => {
            const { employeeId, month } = event.detail || {};
            console.log(`تم تحديث الحضور للموظف ${employeeId} في الشهر ${month}`);

            // If the update is for the current month, refresh payroll data
            if (month === this.currentMonth) {
                this.refreshPayrollForEmployee(employeeId);
            }
        });

        // Listen for bulk attendance updates
        document.addEventListener('attendanceBulkUpdated', (event) => {
            const { month } = event.detail || {};
            console.log(`تحديث مجمع للحضور للشهر ${month}`);

            if (month === this.currentMonth) {
                this.loadPayrollData();
            }
        });
    }

    refreshPayrollForEmployee(employeeId) {
        console.log(`تحديث بيانات الراتب للموظف ${employeeId}...`);

        // Find existing payroll for this employee and month
        const payrolls = Database.getAll('payroll') || [];
        const existingPayroll = payrolls.find(p =>
            p.employee_id === employeeId && p.month === this.currentMonth
        );

        if (existingPayroll) {
            // Recalculate and update the payroll
            const employee = Database.getEmployee(employeeId);
            if (employee) {
                const attendance = Database.getAttendance({
                    employee_id: employeeId,
                    month: this.currentMonth
                });

                const workingDays = this.getWorkingDaysInMonth(this.currentMonth, employee);
                const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, this.currentMonth, employee);

                // Update attendance days in the payroll record
                const updatedData = {
                    ...existingPayroll,
                    working_days: workingDays,
                    attendance_days: attendanceStats.presentDays,
                    updated_at: new Date().toISOString()
                };

                // Recalculate salary based on new attendance
                const basicSalary = employee.salary || 0;
                const proportionalSalary = workingDays > 0 ?
                    (basicSalary * attendanceStats.presentDays / workingDays) :
                    basicSalary;

                // Recalculate totals
                const allowances = this.calculateAllowances(updatedData);
                const bonuses = this.calculateBonuses(updatedData);
                const grossSalary = proportionalSalary + allowances + bonuses;

                const totalDeductions = (updatedData.social_insurance || 0) +
                                      (updatedData.income_tax || 0) +
                                      (updatedData.advances || 0) +
                                      (updatedData.lateness_deduction || 0) +
                                      (updatedData.absence_deduction || 0) +
                                      (updatedData.other_deductions || 0);

                updatedData.gross_salary = grossSalary;
                updatedData.total_deductions = totalDeductions;
                updatedData.net_salary = Math.max(0, grossSalary - totalDeductions);

                // Save the updated payroll
                Database.update('payroll', existingPayroll.id, updatedData);

                // Refresh the display
                this.loadPayrollData();

                window.samApp.showAlert(
                    `تم تحديث راتب ${employee.name} تلقائياً بناءً على تحديث الحضور`,
                    'info'
                );
            }
        }
    }

    calculatePayroll() {
        const form = document.getElementById('payrollForm');
        if (!form) {
            console.error('Payroll form not found');
            return;
        }

        console.log('Starting payroll calculation...'); // للتشخيص

        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        console.log('Form data:', data); // للتشخيص

        // Convert to numbers with proper validation and ensure non-negative values
        const basicSalary = Math.max(0, parseFloat(data.basic_salary) || 0);
        const workingDays = Math.max(0, parseFloat(data.working_days) || 0);
        const attendanceDays = Math.max(0, Math.min(workingDays, parseFloat(data.attendance_days) || 0));

        // Calculate proportional basic salary with proper rounding
        const proportionalSalary = workingDays > 0 ?
            Math.round((basicSalary * attendanceDays / workingDays) * 100) / 100 :
            basicSalary;

        // Calculate allowances (البدلات) with validation
        const housingAllowance = Math.max(0, parseFloat(data.housing_allowance) || 0);
        const transportAllowance = Math.max(0, parseFloat(data.transport_allowance) || 0);
        const foodAllowance = Math.max(0, parseFloat(data.food_allowance) || 0);
        const otherAllowances = Math.max(0, parseFloat(data.other_allowances) || 0);
        const totalAllowances = Math.round((housingAllowance + transportAllowance + foodAllowance + otherAllowances) * 100) / 100;

        // Calculate bonuses (الحوافز والمكافآت) with validation
        const performanceBonus = Math.max(0, parseFloat(data.performance_bonus) || 0);
        const overtimeHours = Math.max(0, parseFloat(data.overtime_hours) || 0);
        const otherBonuses = Math.max(0, parseFloat(data.other_bonuses) || 0);

        // حساب قيمة الساعات الإضافية مع التقريب الصحيح
        const overtimeRate = this.getOvertimeRate(basicSalary);
        const overtimePay = Math.round((overtimeHours * overtimeRate) * 100) / 100;
        const totalBonuses = Math.round((performanceBonus + overtimePay + otherBonuses) * 100) / 100;

        // Calculate gross salary (إجمالي الاستحقاقات) - الراتب الأساسي + البدلات + الحوافز
        const grossSalary = Math.round((proportionalSalary + totalAllowances + totalBonuses) * 100) / 100;

        // Calculate deductions (الخصومات) with validation
        const socialInsurance = Math.max(0, parseFloat(data.social_insurance) || 0);
        const incomeTax = Math.max(0, parseFloat(data.income_tax) || 0);
        const advances = Math.max(0, parseFloat(data.advances) || 0);
        const latenessDeduction = Math.max(0, parseFloat(data.lateness_deduction) || 0);
        const absenceDeduction = Math.max(0, parseFloat(data.absence_deduction) || 0);
        const otherDeductions = Math.max(0, parseFloat(data.other_deductions) || 0);

        const totalDeductions = Math.round((socialInsurance + incomeTax + advances + latenessDeduction + absenceDeduction + otherDeductions) * 100) / 100;

        // Calculate net salary (صافي الراتب) - الإجمالي - الخصومات
        const netSalary = Math.max(0, Math.round((grossSalary - totalDeductions) * 100) / 100);

        // Update form fields with proper formatting
        const grossSalaryField = form.querySelector('input[name="gross_salary"]');
        const totalDeductionsField = form.querySelector('input[name="total_deductions"]');
        const netSalaryField = form.querySelector('input[name="net_salary"]');

        if (grossSalaryField) {
            grossSalaryField.value = grossSalary.toFixed(2);
            // إضافة تنسيق بصري للقيم الكبيرة
            if (grossSalary >= 10000) {
                grossSalaryField.style.fontWeight = 'bold';
                grossSalaryField.style.color = '#28a745';
            }
        }

        if (totalDeductionsField) {
            totalDeductionsField.value = totalDeductions.toFixed(2);
            // إضافة تنسيق بصري للخصومات العالية
            if (totalDeductions > grossSalary * 0.3) {
                totalDeductionsField.style.color = '#dc3545';
            } else {
                totalDeductionsField.style.color = '';
            }
        }

        if (netSalaryField) {
            netSalaryField.value = netSalary.toFixed(2);
            // إضافة تنسيق بصري لصافي الراتب
            netSalaryField.style.fontWeight = 'bold';
            netSalaryField.style.fontSize = '1.1em';
            if (netSalary <= 0) {
                netSalaryField.style.color = '#dc3545';
            } else {
                netSalaryField.style.color = '#28a745';
            }
        }

        // Store calculated values for later use with detailed breakdown
        this.lastCalculation = {
            basicSalary,
            workingDays,
            attendanceDays,
            proportionalSalary,
            totalAllowances,
            totalBonuses,
            grossSalary,
            totalDeductions,
            netSalary,
            overtimePay,
            overtimeHours,
            overtimeRate,
            // تفاصيل البدلات
            allowancesBreakdown: {
                housing: housingAllowance,
                transport: transportAllowance,
                food: foodAllowance,
                other: otherAllowances
            },
            // تفاصيل الحوافز
            bonusesBreakdown: {
                performance: performanceBonus,
                overtime: overtimePay,
                other: otherBonuses
            },
            // تفاصيل الخصومات
            deductionsBreakdown: {
                socialInsurance,
                incomeTax,
                advances,
                lateness: latenessDeduction,
                absence: absenceDeduction,
                other: otherDeductions
            }
        };

        // إضافة تحديث للملخص إذا كان متاحاً
        this.updateCalculationSummary();

        console.log('Payroll calculation completed:', {
            grossSalary,
            totalDeductions,
            netSalary
        }); // للتشخيص
    }

    updateCalculationSummary() {
        if (!this.lastCalculation) return;

        // البحث عن عنصر الملخص في النموذج
        const summaryElement = document.getElementById('calculationSummary');
        if (summaryElement) {
            const calc = this.lastCalculation;
            summaryElement.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h6 class="card-title text-success">إجمالي الاستحقاقات</h6>
                                <h4 class="text-success">${window.samApp.formatCurrency(calc.grossSalary)}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h6 class="card-title text-danger">إجمالي الخصومات</h6>
                                <h4 class="text-danger">${window.samApp.formatCurrency(calc.totalDeductions)}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary">صافي الراتب</h6>
                                <h4 class="text-primary">${window.samApp.formatCurrency(calc.netSalary)}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    savePayroll() {
        const form = document.getElementById('payrollForm');
        if (!form) {
            window.samApp.showAlert('خطأ في النموذج', 'danger');
            return;
        }

        // Validate required fields
        const employeeId = form.querySelector('select[name="employee_id"]').value;
        const month = form.querySelector('input[name="month"]').value;

        if (!employeeId || !month) {
            window.samApp.showAlert('يرجى تحديد الموظف والشهر', 'warning');
            return;
        }

        // Check for duplicate payroll
        if (!this.selectedPayroll) {
            const existingPayroll = Database.getAll('payroll').find(p =>
                p.employee_id === employeeId && p.month === month
            );
            if (existingPayroll) {
                window.samApp.showAlert('يوجد كشف راتب لهذا الموظف في نفس الشهر', 'warning');
                return;
            }
        }

        const formData = new FormData(form);
        const payrollData = Object.fromEntries(formData.entries());

        // Convert numeric fields with validation
        const numericFields = ['basic_salary', 'working_days', 'attendance_days', 'housing_allowance',
                              'transport_allowance', 'food_allowance', 'other_allowances', 'performance_bonus',
                              'overtime_hours', 'other_bonuses', 'social_insurance', 'income_tax', 'advances',
                              'lateness_deduction', 'absence_deduction', 'other_deductions',
                              'gross_salary', 'total_deductions', 'net_salary'];

        numericFields.forEach(field => {
            const value = parseFloat(payrollData[field]);
            payrollData[field] = isNaN(value) ? 0 : value;
        });

        // Ensure calculations are correct before saving
        this.calculatePayroll();

        // Update with latest calculated values
        if (this.lastCalculation) {
            payrollData.gross_salary = this.lastCalculation.grossSalary;
            payrollData.total_deductions = this.lastCalculation.totalDeductions;
            payrollData.net_salary = this.lastCalculation.netSalary;
        }

        try {
            // إظهار مؤشر التحميل
            const saveBtn = document.getElementById('savePayrollBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            saveBtn.disabled = true;

            if (this.selectedPayroll) {
                // Update existing payroll
                payrollData.updated_at = new Date().toISOString();
                Database.update('payroll', this.selectedPayroll.id, payrollData);

                // إشعار مفصل
                const employee = Database.getEmployee(payrollData.employee_id);
                window.samApp.showAlert(
                    `تم تحديث كشف راتب ${employee?.name || 'الموظف'} لشهر ${this.formatMonth(payrollData.month)} بنجاح`,
                    'success'
                );
            } else {
                // Create new payroll
                payrollData.created_at = new Date().toISOString();
                payrollData.updated_at = new Date().toISOString();
                Database.create('payroll', payrollData);

                // إشعار مفصل
                const employee = Database.getEmployee(payrollData.employee_id);
                window.samApp.showAlert(
                    `تم إضافة كشف راتب ${employee?.name || 'الموظف'} لشهر ${this.formatMonth(payrollData.month)} بنجاح`,
                    'success'
                );
            }

            // إخفاء النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('payrollModal'));
            if (modal) modal.hide();

            // تحديث البيانات
            this.loadPayrollData();

            // إعادة تعيين الزر
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;

        } catch (error) {
            console.error('Error saving payroll:', error);
            window.samApp.showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'danger');

            // إعادة تعيين الزر في حالة الخطأ
            const saveBtn = document.getElementById('savePayrollBtn');
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ';
            saveBtn.disabled = false;
        }
    }

    generateMonthlyPayroll() {
        const month = prompt('أدخل الشهر (YYYY-MM):', this.currentMonth);
        if (!month) return;

        // Validate month format
        if (!/^\d{4}-\d{2}$/.test(month)) {
            window.samApp.showAlert('تنسيق الشهر غير صحيح. يجب أن يكون بالتنسيق YYYY-MM', 'danger');
            return;
        }

        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        if (employees.length === 0) {
            window.samApp.showAlert('لا يوجد موظفون نشطون لإنشاء كشوف رواتب', 'warning');
            return;
        }

        const existingPayrolls = Database.getAll('payroll').filter(p => p.month === month);

        if (existingPayrolls.length > 0) {
            if (!confirm(`يوجد ${existingPayrolls.length} كشف راتب لهذا الشهر. هل تريد المتابعة وإنشاء كشوف للموظفين المتبقين؟`)) {
                return;
            }
        }

        let generatedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;

        employees.forEach(employee => {
            try {
                // Check if payroll already exists for this employee and month
                const existing = existingPayrolls.find(p => p.employee_id === employee.id);
                if (existing) {
                    skippedCount++;
                    return;
                }

                // Validate employee data
                if (!employee.salary || employee.salary <= 0) {
                    console.warn(`Employee ${employee.name} has no valid salary`);
                    errorCount++;
                    return;
                }

                // Get attendance data
                const attendance = Database.getAttendance({ employee_id: employee.id, month });
                const workingDays = this.getWorkingDaysInMonth(month, employee);
                const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, month, employee);
                const attendanceDays = attendanceStats.presentDays;

                // Calculate basic payroll data
                const basicSalary = employee.salary || 0;
                const proportionalSalary = workingDays > 0 ? (basicSalary * attendanceDays / workingDays) : basicSalary;

                // Get employee-specific allowances and deductions
                let calculations = null;
                if (window.overtimeCalculator) {
                    calculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employee.id, month);
                }

                // Basic deductions
                const socialInsurance = basicSalary * 0.09; // 9% social insurance
                const incomeTax = this.calculateIncomeTax(basicSalary);

                // Calculate allowances and bonuses separately
                const housingAllowance = calculations?.allowances.housing || 0;
                const transportAllowance = calculations?.allowances.transport || 0;
                const otherAllowances = calculations?.allowances.other || 0;
                const overtimeHours = calculations?.details.overtime.hours || 0;

                // Calculate deductions
                const advancesDeduction = calculations?.deductions.advances || 0;
                const latenessDeduction = calculations?.deductions.lateness || 0;
                const absenceDeduction = calculations?.deductions.absence || 0;
                const penaltiesDeduction = calculations?.deductions.penalties || 0;

                // Calculate totals
                const totalAllowances = housingAllowance + transportAllowance + otherAllowances;
                const overtimePay = overtimeHours * this.getOvertimeRate(basicSalary);
                const grossSalary = proportionalSalary + totalAllowances + overtimePay;
                const totalDeductions = socialInsurance + incomeTax + advancesDeduction + latenessDeduction + absenceDeduction + penaltiesDeduction;
                const netSalary = Math.max(0, grossSalary - totalDeductions);

                const payrollData = {
                    employee_id: employee.id,
                    month: month,
                    basic_salary: basicSalary,
                    working_days: workingDays,
                    attendance_days: attendanceDays,
                    housing_allowance: housingAllowance,
                    transport_allowance: transportAllowance,
                    food_allowance: 0,
                    other_allowances: otherAllowances,
                    performance_bonus: 0,
                    overtime_hours: overtimeHours,
                    other_bonuses: 0,
                    social_insurance: socialInsurance,
                    income_tax: incomeTax,
                    advances: advancesDeduction,
                    lateness_deduction: latenessDeduction,
                    absence_deduction: absenceDeduction,
                    other_deductions: penaltiesDeduction,
                    gross_salary: Math.round(grossSalary * 100) / 100,
                    total_deductions: Math.round(totalDeductions * 100) / 100,
                    net_salary: Math.round(netSalary * 100) / 100,
                    status: 'pending',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                Database.create('payroll', payrollData);
                generatedCount++;
            } catch (error) {
                console.error(`Error generating payroll for employee ${employee.name}:`, error);
                errorCount++;
            }
        });

        // Show summary message
        let message = `تم إنشاء ${generatedCount} كشف راتب بنجاح`;
        if (skippedCount > 0) {
            message += `، تم تخطي ${skippedCount} موظف (يوجد كشف راتب مسبقاً)`;
        }
        if (errorCount > 0) {
            message += `، فشل في إنشاء ${errorCount} كشف راتب`;
        }

        const alertType = errorCount > 0 ? 'warning' : 'success';
        window.samApp.showAlert(message, alertType);

        this.loadPayrollData();
    }



    viewPayroll(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        this.showPayrollModal(payroll);
    }

    markAsPaid(payrollId) {
        try {
            const payroll = Database.getById('payroll', payrollId);
            if (!payroll) {
                window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
                return;
            }

            const updatedData = {
                ...payroll,
                status: 'paid',
                payment_date: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString()
            };

            Database.update('payroll', payrollId, updatedData);
            window.samApp.showAlert('تم تحديد الراتب كمدفوع بنجاح', 'success');
            this.loadPayrollData();
        } catch (error) {
            window.samApp.showAlert('حدث خطأ: ' + error.message, 'danger');
        }
    }

    deletePayroll(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        if (!payroll) {
            window.samApp.showAlert('كشف الراتب غير موجود', 'danger');
            return;
        }

        const employee = Database.getEmployee(payroll.employee_id);
        const employeeName = employee?.name || 'الموظف';
        const monthName = this.formatMonth(payroll.month);

        if (confirm(`هل أنت متأكد من حذف كشف راتب ${employeeName} لشهر ${monthName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            try {
                Database.delete('payroll', payrollId);
                window.samApp.showAlert(`تم حذف كشف راتب ${employeeName} بنجاح`, 'success');
                this.loadPayrollData();
            } catch (error) {
                console.error('Error deleting payroll:', error);
                window.samApp.showAlert('حدث خطأ أثناء حذف كشف الراتب: ' + error.message, 'danger');
            }
        }
    }

    printPayslip(payrollId) {
        const payroll = Database.getById('payroll', payrollId);
        const employee = Database.getEmployee(payroll.employee_id);
        const settings = Database.getSettings();

        if (!payroll || !employee) {
            window.samApp.showAlert('البيانات غير متوفرة', 'danger');
            return;
        }

        // Create payslip HTML
        const payslipHTML = this.generatePayslipHTML(payroll, employee, settings);

        // Open print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(payslipHTML);
        printWindow.document.close();
        printWindow.print();
    }

    generatePayslipHTML(payroll, employee, settings) {
        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>قسيمة راتب - ${employee.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
                    .employee-info { margin: 20px 0; }
                    .payroll-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .payroll-table th, .payroll-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    .payroll-table th { background-color: #f5f5f5; }
                    .total-row { font-weight: bold; background-color: #e9ecef; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${settings.company.name}</h1>
                    <h2>قسيمة راتب</h2>
                    <p>شهر: ${this.formatMonth(payroll.month)}</p>
                </div>

                <div class="employee-info">
                    <p><strong>اسم الموظف:</strong> ${employee.name}</p>
                    <p><strong>الرقم الوظيفي:</strong> ${employee.employee_number}</p>
                    <p><strong>القسم:</strong> ${employee.department}</p>
                </div>

                <table class="payroll-table">
                    <tr><th colspan="2" style="background-color: #007bff; color: white;">الاستحقاقات</th></tr>
                    <tr><td>الراتب الأساسي</td><td>${window.samApp.formatCurrency(payroll.basic_salary)}</td></tr>
                    <tr><td>بدل سكن</td><td>${window.samApp.formatCurrency(payroll.housing_allowance || 0)}</td></tr>
                    <tr><td>بدل مواصلات</td><td>${window.samApp.formatCurrency(payroll.transport_allowance || 0)}</td></tr>
                    <tr><td>بدل طعام</td><td>${window.samApp.formatCurrency(payroll.food_allowance || 0)}</td></tr>
                    <tr><td>بدلات أخرى</td><td>${window.samApp.formatCurrency(payroll.other_allowances || 0)}</td></tr>
                    <tr><td>ساعات إضافية</td><td>${window.samApp.formatCurrency(payroll.overtime || 0)}</td></tr>
                    <tr><td>حافز الأداء</td><td>${window.samApp.formatCurrency(payroll.performance_bonus || 0)}</td></tr>
                    <tr><td>حوافز أخرى</td><td>${window.samApp.formatCurrency(payroll.other_bonuses || 0)}</td></tr>
                    <tr class="total-row"><td><strong>إجمالي الاستحقاقات</strong></td><td><strong>${window.samApp.formatCurrency(payroll.gross_salary)}</strong></td></tr>

                    <tr><th colspan="2" style="background-color: #dc3545; color: white;">الخصومات</th></tr>
                    <tr><td>التأمينات الاجتماعية</td><td>(${window.samApp.formatCurrency(payroll.social_insurance || 0)})</td></tr>
                    <tr><td>ضريبة الدخل</td><td>(${window.samApp.formatCurrency(payroll.income_tax || 0)})</td></tr>
                    <tr><td>السلف</td><td>(${window.samApp.formatCurrency(payroll.advances || 0)})</td></tr>
                    <tr><td>خصم التأخير</td><td>(${window.samApp.formatCurrency(payroll.lateness_deduction || 0)})</td></tr>
                    <tr><td>خصم الغياب</td><td>(${window.samApp.formatCurrency(payroll.absence_deduction || 0)})</td></tr>
                    <tr><td>جزاءات وخصومات أخرى</td><td>(${window.samApp.formatCurrency(payroll.other_deductions || 0)})</td></tr>
                    <tr class="total-row"><td><strong>إجمالي الخصومات</strong></td><td><strong>(${window.samApp.formatCurrency(payroll.total_deductions)})</strong></td></tr>

                    <tr style="background-color: #28a745; color: white;"><td><strong>صافي الراتب</strong></td><td><strong>${window.samApp.formatCurrency(payroll.net_salary)}</strong></td></tr>
                </table>

                ${this.payrollDetails ? this.generateDetailedBreakdown(this.payrollDetails) : ''}

                <div style="margin-top: 40px;">
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
            </body>
            </html>
        `;
    }

    generateDetailedBreakdown(details) {
        return `
            <div style="margin-top: 30px; page-break-inside: avoid;">
                <h3 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px;">تفصيل مفردات الراتب</h3>

                ${details.overtime.hours > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-right: 4px solid #28a745;">
                    <h4 style="color: #28a745; margin-bottom: 10px;">الساعات الإضافية</h4>
                    <p><strong>عدد الساعات:</strong> ${details.overtime.hours} ساعة</p>
                    <p><strong>عدد الأيام:</strong> ${details.overtime.days} يوم</p>
                    <p><strong>المبلغ:</strong> ${window.samApp.formatCurrency(details.overtime.amount)}</p>
                </div>
                ` : ''}

                ${details.undertime.hours > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #fff3cd; border-right: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">نقص في ساعات العمل</h4>
                    <p><strong>عدد الساعات:</strong> ${details.undertime.hours} ساعة</p>
                    <p><strong>عدد الأيام:</strong> ${details.undertime.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.undertime.amount)}</p>
                </div>
                ` : ''}

                ${details.lateness.days > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">التأخير</h4>
                    <p><strong>إجمالي دقائق التأخير:</strong> ${details.lateness.minutes} دقيقة</p>
                    <p><strong>عدد أيام التأخير:</strong> ${details.lateness.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.lateness.amount)}</p>
                </div>
                ` : ''}

                ${details.absence.days > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">الغياب</h4>
                    <p><strong>عدد أيام الغياب:</strong> ${details.absence.days} يوم</p>
                    <p><strong>المبلغ المخصوم:</strong> ${window.samApp.formatCurrency(details.absence.amount)}</p>
                </div>
                ` : ''}

                ${details.advances.count > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #e2e3e5; border-right: 4px solid #6c757d;">
                    <h4 style="color: #495057; margin-bottom: 10px;">السلف</h4>
                    <p><strong>عدد السلف النشطة:</strong> ${details.advances.count}</p>
                    <p><strong>إجمالي الأقساط الشهرية:</strong> ${window.samApp.formatCurrency(details.advances.amount)}</p>
                </div>
                ` : ''}

                ${details.penalties.count > 0 ? `
                <div style="margin-bottom: 15px; padding: 10px; background-color: #f8d7da; border-right: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-bottom: 10px;">الجزاءات</h4>
                    <p><strong>عدد الجزاءات:</strong> ${details.penalties.count}</p>
                    <p><strong>إجمالي المبلغ:</strong> ${window.samApp.formatCurrency(details.penalties.amount)}</p>
                </div>
                ` : ''}
            </div>
        `;
    }

    exportPayroll() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        const employees = Database.getEmployees();

        const exportData = payrolls.map(payroll => {
            const employee = employees.find(emp => emp.id === payroll.employee_id);
            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'الشهر': this.formatMonth(payroll.month),
                'الراتب الأساسي': payroll.basic_salary,
                'البدلات': this.calculateAllowances(payroll),
                'الحوافز': this.calculateBonuses(payroll),
                'إجمالي الاستحقاقات': payroll.gross_salary,
                'الخصومات': payroll.total_deductions,
                'صافي الراتب': payroll.net_salary,
                'الحالة': payroll.status,
                'تاريخ الدفع': payroll.payment_date || '',
                'ملاحظات': payroll.notes || ''
            };
        });

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الرواتب');
        XLSX.writeFile(wb, `payroll_${this.currentMonth}.xlsx`);

        window.samApp.showAlert('تم تصدير بيانات الرواتب بنجاح', 'success');
    }

    printPayrollSummary() {
        const payrolls = Database.getAll('payroll') || [];
        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد كشوف رواتب للشهر المحدد', 'warning');
            return;
        }

        window.printManager.printPayrollReport(monthPayrolls, this.currentMonth);
    }

    // تنظيف الموارد عند مغادرة الصفحة
    cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    // دالة لإعادة حساب جميع كشوف الرواتب في الشهر المحدد
    recalculateMonthlyPayrolls() {
        const payrolls = Database.getAll('payroll').filter(p => p.month === this.currentMonth);
        let updatedCount = 0;

        payrolls.forEach(payroll => {
            const employee = Database.getEmployee(payroll.employee_id);
            if (!employee) return;

            // إعادة حساب البيانات
            const attendance = Database.getAttendance({
                employee_id: payroll.employee_id,
                month: payroll.month
            });

            const workingDays = this.getWorkingDaysInMonth(payroll.month, employee);
            const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, payroll.month, employee);
            const attendanceDays = attendanceStats.presentDays;

            // تحديث البيانات المحسوبة
            const basicSalary = employee.salary || 0;
            const proportionalSalary = workingDays > 0 ? (basicSalary * attendanceDays / workingDays) : basicSalary;

            const updatedData = {
                ...payroll,
                basic_salary: basicSalary,
                working_days: workingDays,
                attendance_days: attendanceDays,
                updated_at: new Date().toISOString()
            };

            // إعادة حساب الإجمالي والصافي
            const allowances = this.calculateAllowances(updatedData);
            const bonuses = this.calculateBonuses(updatedData);
            const grossSalary = proportionalSalary + allowances + bonuses;

            const totalDeductions = (updatedData.social_insurance || 0) +
                                  (updatedData.income_tax || 0) +
                                  (updatedData.advances || 0) +
                                  (updatedData.lateness_deduction || 0) +
                                  (updatedData.absence_deduction || 0) +
                                  (updatedData.other_deductions || 0);

            updatedData.gross_salary = grossSalary;
            updatedData.total_deductions = totalDeductions;
            updatedData.net_salary = Math.max(0, grossSalary - totalDeductions);

            Database.update('payroll', payroll.id, updatedData);
            updatedCount++;
        });

        if (updatedCount > 0) {
            window.samApp.showAlert(`تم إعادة حساب ${updatedCount} كشف راتب بنجاح`, 'success');
            this.loadPayrollData();
        } else {
            window.samApp.showAlert('لا توجد كشوف رواتب للتحديث', 'info');
        }
    }

    // إنشاء كشف راتب لموظف محدد
    createPayrollForEmployee(employeeId, month) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) {
            window.samApp.showAlert('الموظف غير موجود', 'danger');
            return;
        }

        // فتح نموذج إضافة راتب مع بيانات الموظف
        this.showPayrollModal();

        // ملء البيانات الأساسية
        const form = document.getElementById('payrollForm');
        form.querySelector('select[name="employee_id"]').value = employeeId;
        form.querySelector('input[name="month"]').value = month;

        // تحميل بيانات الموظف
        this.loadEmployeeData(employeeId);
    }

    showTotalSummary() {
        try {
            console.log('Starting showTotalSummary...');

            // التحقق من وجود النموذج
            const modalElement = document.getElementById('totalSummaryModal');
            if (!modalElement) {
                throw new Error('نموذج إجمالي الرواتب غير موجود');
            }

            const modal = new bootstrap.Modal(modalElement);

            // التحقق من وجود عنصر العنوان
            const titleElement = document.getElementById('summaryMonthTitle');
            if (titleElement) {
                titleElement.textContent = this.formatMonth(this.currentMonth);
            } else {
                console.warn('Summary month title element not found');
            }

            // تحميل البيانات وعرض الملخص
            this.loadTotalSummaryData();

            // عرض النموذج
            modal.show();

            // ربط أحداث التصدير والطباعة (مرة واحدة فقط)
            if (!this.totalSummaryEventsBound) {
                this.bindTotalSummaryEvents();
                this.totalSummaryEventsBound = true;
            }

            console.log('Total summary modal shown successfully');

        } catch (error) {
            console.error('Error in showTotalSummary:', error);
            window.samApp.showAlert('حدث خطأ في عرض إجمالي الرواتب: ' + error.message, 'danger');
        }
    }

    loadTotalSummaryData() {
        try {
            console.log('Loading total summary data for month:', this.currentMonth);

            const payrolls = Database.getAll('payroll') || [];
            const employees = Database.getAll('employees') || [];
            const departments = Database.getAll('departments') || [];

            console.log('Data loaded:', { payrolls: payrolls.length, employees: employees.length, departments: departments.length });

            // تصفية الرواتب للشهر المحدد
            const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);
            console.log('Month payrolls found:', monthPayrolls.length);

            if (monthPayrolls.length === 0) {
                console.log('No payrolls found for current month, showing empty summary');
                this.showEmptyTotalSummary();
                return;
            }

            // حساب الإجماليات
            const summary = this.calculateTotalSummary(monthPayrolls, employees, departments);
            console.log('Summary calculated:', summary);

            // عرض البطاقات الإجمالية
            this.renderSummaryCards(summary);

            // عرض تفصيل الاستحقاقات والخصومات
            this.renderBreakdowns(summary);

            // عرض جدول تفاصيل الموظفين
            this.renderEmployeeDetailsTable(monthPayrolls, employees, departments, summary);

            console.log('Total summary data loaded successfully');

        } catch (error) {
            console.error('Error loading total summary data:', error);
            this.showEmptyTotalSummary();
            window.samApp.showAlert('حدث خطأ في تحميل بيانات إجمالي الرواتب: ' + error.message, 'warning');
        }
    }

    calculateTotalSummary(payrolls, employees, departments) {
        let summary = {
            totalEmployees: payrolls.length,
            totalBasicSalary: 0,
            totalAllowances: 0,
            totalBonuses: 0,
            totalGrossSalary: 0,
            totalDeductions: 0,
            totalNetSalary: 0,
            allowancesBreakdown: {
                housing: 0,
                transport: 0,
                food: 0,
                other: 0
            },
            bonusesBreakdown: {
                performance: 0,
                overtime: 0,
                other: 0
            },
            deductionsBreakdown: {
                socialInsurance: 0,
                incomeTax: 0,
                advances: 0,
                lateness: 0,
                absence: 0,
                other: 0
            },
            statusBreakdown: {
                paid: 0,
                pending: 0,
                cancelled: 0
            },
            departmentBreakdown: {}
        };

        payrolls.forEach(payroll => {
            const employee = employees.find(e => e.id === payroll.employee_id);
            const department = departments.find(d => d.id === employee?.department);

            // الإجماليات الأساسية
            summary.totalBasicSalary += payroll.basic_salary || 0;
            summary.totalGrossSalary += payroll.gross_salary || 0;
            summary.totalDeductions += payroll.total_deductions || 0;
            summary.totalNetSalary += payroll.net_salary || 0;

            // تفصيل البدلات
            summary.allowancesBreakdown.housing += payroll.housing_allowance || 0;
            summary.allowancesBreakdown.transport += payroll.transport_allowance || 0;
            summary.allowancesBreakdown.food += payroll.food_allowance || 0;
            summary.allowancesBreakdown.other += payroll.other_allowances || 0;
            summary.totalAllowances += this.calculateAllowances(payroll);

            // تفصيل الحوافز
            summary.bonusesBreakdown.performance += payroll.performance_bonus || 0;
            summary.bonusesBreakdown.overtime += this.calculateOvertimePay(payroll);
            summary.bonusesBreakdown.other += payroll.other_bonuses || 0;
            summary.totalBonuses += this.calculateBonuses(payroll);

            // تفصيل الخصومات
            summary.deductionsBreakdown.socialInsurance += payroll.social_insurance || 0;
            summary.deductionsBreakdown.incomeTax += payroll.income_tax || 0;
            summary.deductionsBreakdown.advances += payroll.advances || 0;
            summary.deductionsBreakdown.lateness += payroll.lateness_deduction || 0;
            summary.deductionsBreakdown.absence += payroll.absence_deduction || 0;
            summary.deductionsBreakdown.other += payroll.other_deductions || 0;

            // تفصيل الحالات
            summary.statusBreakdown[payroll.status] = (summary.statusBreakdown[payroll.status] || 0) + 1;

            // تفصيل الأقسام
            const deptName = department?.name || 'غير محدد';
            if (!summary.departmentBreakdown[deptName]) {
                summary.departmentBreakdown[deptName] = {
                    count: 0,
                    totalBasic: 0,
                    totalNet: 0
                };
            }
            summary.departmentBreakdown[deptName].count++;
            summary.departmentBreakdown[deptName].totalBasic += payroll.basic_salary || 0;
            summary.departmentBreakdown[deptName].totalNet += payroll.net_salary || 0;
        });

        return summary;
    }

    renderSummaryCards(summary) {
        try {
            const cardsContainer = document.getElementById('totalSummaryCards');
            if (!cardsContainer) {
                throw new Error('عنصر البطاقات الإجمالية غير موجود');
            }

            // دالة مساعدة لتنسيق العملة
            const formatCurrency = (amount) => {
                try {
                    return window.samApp.formatCurrency(amount);
                } catch (error) {
                    return `${parseFloat(amount || 0).toLocaleString('ar-SA')} ر.س`;
                }
            };

            cardsContainer.innerHTML = `
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${summary.totalEmployees || 0}</h3>
                            <p class="mb-0">عدد الموظفين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${formatCurrency(summary.totalBasicSalary)}</h3>
                            <p class="mb-0">إجمالي الرواتب الأساسية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${formatCurrency(summary.totalGrossSalary)}</h3>
                            <p class="mb-0">إجمالي الاستحقاقات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>${formatCurrency(summary.totalNetSalary)}</h3>
                            <p class="mb-0">إجمالي صافي الرواتب</p>
                        </div>
                    </div>
                </div>
            `;

            console.log('Summary cards rendered successfully');

        } catch (error) {
            console.error('Error rendering summary cards:', error);
            const cardsContainer = document.getElementById('totalSummaryCards');
            if (cardsContainer) {
                cardsContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ في عرض البطاقات الإجمالية: ${error.message}
                        </div>
                    </div>
                `;
            }
        }
    }

    renderBreakdowns(summary) {
        // تفصيل الاستحقاقات
        const allowancesContainer = document.getElementById('allowancesBreakdown');
        allowancesContainer.innerHTML = `
            <div class="row">
                <div class="col-6 mb-2">
                    <strong>الرواتب الأساسية:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.totalBasicSalary)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>بدل السكن:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.allowancesBreakdown.housing)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>بدل المواصلات:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.allowancesBreakdown.transport)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>بدل الطعام:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.allowancesBreakdown.food)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>حافز الأداء:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.bonusesBreakdown.performance)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>الساعات الإضافية:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.bonusesBreakdown.overtime)}</span>
                </div>
                <div class="col-12">
                    <hr>
                    <strong class="text-success">إجمالي الاستحقاقات:</strong>
                    <span class="float-end text-success fw-bold">${window.samApp.formatCurrency(summary.totalGrossSalary)}</span>
                </div>
            </div>
        `;

        // تفصيل الخصومات
        const deductionsContainer = document.getElementById('deductionsBreakdown');
        deductionsContainer.innerHTML = `
            <div class="row">
                <div class="col-6 mb-2">
                    <strong>التأمينات الاجتماعية:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.socialInsurance)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>ضريبة الدخل:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.incomeTax)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>السلف:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.advances)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>خصم التأخير:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.lateness)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>خصم الغياب:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.absence)}</span>
                </div>
                <div class="col-6 mb-2">
                    <strong>خصومات أخرى:</strong>
                    <span class="float-end">${window.samApp.formatCurrency(summary.deductionsBreakdown.other)}</span>
                </div>
                <div class="col-12">
                    <hr>
                    <strong class="text-danger">إجمالي الخصومات:</strong>
                    <span class="float-end text-danger fw-bold">${window.samApp.formatCurrency(summary.totalDeductions)}</span>
                </div>
            </div>
        `;
    }

    renderEmployeeDetailsTable(payrolls, employees, departments, summary) {
        const tableBody = document.getElementById('employeeDetailsTable');
        const totalRow = document.getElementById('totalSummaryRow');

        let tableHTML = '';

        payrolls.forEach(payroll => {
            const employee = employees.find(e => e.id === payroll.employee_id);
            const department = departments.find(d => d.id === employee?.department);

            const allowances = this.calculateAllowances(payroll);
            const bonuses = this.calculateBonuses(payroll);

            const statusClass = {
                'paid': 'success',
                'pending': 'warning',
                'cancelled': 'danger'
            }[payroll.status] || 'secondary';

            const statusText = {
                'paid': 'مدفوعة',
                'pending': 'في الانتظار',
                'cancelled': 'ملغية'
            }[payroll.status] || payroll.status;

            tableHTML += `
                <tr>
                    <td>
                        <div class="fw-bold">${employee?.name || 'غير معروف'}</div>
                        <small class="text-muted">${employee?.employee_number || ''}</small>
                    </td>
                    <td>${department?.name || 'غير محدد'}</td>
                    <td class="text-end">${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                    <td class="text-end text-success">${window.samApp.formatCurrency(allowances)}</td>
                    <td class="text-end text-info">${window.samApp.formatCurrency(bonuses)}</td>
                    <td class="text-end fw-bold">${window.samApp.formatCurrency(payroll.gross_salary || 0)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                    <td class="text-end fw-bold text-primary">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                    <td>
                        <span class="badge bg-${statusClass}">${statusText}</span>
                    </td>
                </tr>
            `;
        });

        tableBody.innerHTML = tableHTML;

        // صف الإجمالي
        totalRow.innerHTML = `
            <th class="text-center">الإجمالي (${summary.totalEmployees} موظف)</th>
            <th></th>
            <th class="text-end">${window.samApp.formatCurrency(summary.totalBasicSalary)}</th>
            <th class="text-end text-success">${window.samApp.formatCurrency(summary.totalAllowances)}</th>
            <th class="text-end text-info">${window.samApp.formatCurrency(summary.totalBonuses)}</th>
            <th class="text-end fw-bold">${window.samApp.formatCurrency(summary.totalGrossSalary)}</th>
            <th class="text-end text-danger">${window.samApp.formatCurrency(summary.totalDeductions)}</th>
            <th class="text-end fw-bold text-primary">${window.samApp.formatCurrency(summary.totalNetSalary)}</th>
            <th></th>
        `;
    }

    showEmptyTotalSummary() {
        document.getElementById('totalSummaryCards').innerHTML = `
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <h5>لا توجد كشوف رواتب للشهر المحدد</h5>
                    <p>يرجى إنشاء كشوف الرواتب أولاً أو اختيار شهر آخر</p>
                </div>
            </div>
        `;

        document.getElementById('allowancesBreakdown').innerHTML = '<p class="text-muted">لا توجد بيانات</p>';
        document.getElementById('deductionsBreakdown').innerHTML = '<p class="text-muted">لا توجد بيانات</p>';
        document.getElementById('employeeDetailsTable').innerHTML = '';
        document.getElementById('totalSummaryRow').innerHTML = '';
    }

    bindTotalSummaryEvents() {
        // تصدير التقرير
        document.getElementById('exportTotalSummaryBtn').addEventListener('click', () => {
            this.exportTotalSummary();
        });

        // طباعة التقرير
        document.getElementById('printTotalSummaryBtn').addEventListener('click', () => {
            this.printTotalSummary();
        });
    }

    exportTotalSummary() {
        const payrolls = Database.getAll('payroll') || [];
        const employees = Database.getAll('employees') || [];
        const departments = Database.getAll('departments') || [];

        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const summary = this.calculateTotalSummary(monthPayrolls, employees, departments);

        // إعداد بيانات التصدير
        const summaryData = [
            { 'البيان': 'عدد الموظفين', 'القيمة': summary.totalEmployees },
            { 'البيان': 'إجمالي الرواتب الأساسية', 'القيمة': summary.totalBasicSalary },
            { 'البيان': 'إجمالي البدلات', 'القيمة': summary.totalAllowances },
            { 'البيان': 'إجمالي الحوافز', 'القيمة': summary.totalBonuses },
            { 'البيان': 'إجمالي الاستحقاقات', 'القيمة': summary.totalGrossSalary },
            { 'البيان': 'إجمالي الخصومات', 'القيمة': summary.totalDeductions },
            { 'البيان': 'إجمالي صافي الرواتب', 'القيمة': summary.totalNetSalary }
        ];

        const detailsData = monthPayrolls.map(payroll => {
            const employee = employees.find(e => e.id === payroll.employee_id);
            const department = departments.find(d => d.id === employee?.department);

            return {
                'الموظف': employee?.name || 'غير معروف',
                'الرقم الوظيفي': employee?.employee_number || '',
                'القسم': department?.name || 'غير محدد',
                'الراتب الأساسي': payroll.basic_salary || 0,
                'البدلات': this.calculateAllowances(payroll),
                'الحوافز': this.calculateBonuses(payroll),
                'إجمالي الاستحقاقات': payroll.gross_salary || 0,
                'الخصومات': payroll.total_deductions || 0,
                'صافي الراتب': payroll.net_salary || 0,
                'الحالة': payroll.status
            };
        });

        // إنشاء ملف Excel
        const wb = XLSX.utils.book_new();

        // ورقة الملخص
        const summaryWS = XLSX.utils.json_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(wb, summaryWS, 'الملخص الإجمالي');

        // ورقة التفاصيل
        const detailsWS = XLSX.utils.json_to_sheet(detailsData);
        XLSX.utils.book_append_sheet(wb, detailsWS, 'تفاصيل الموظفين');

        // حفظ الملف
        XLSX.writeFile(wb, `total_payroll_summary_${this.currentMonth}.xlsx`);

        window.samApp.showAlert('تم تصدير التقرير الإجمالي بنجاح', 'success');
    }

    printTotalSummary() {
        const payrolls = Database.getAll('payroll') || [];
        const employees = Database.getAll('employees') || [];
        const departments = Database.getAll('departments') || [];

        const monthPayrolls = payrolls.filter(p => p.month === this.currentMonth);

        if (monthPayrolls.length === 0) {
            window.samApp.showAlert('لا توجد بيانات للطباعة', 'warning');
            return;
        }

        const summary = this.calculateTotalSummary(monthPayrolls, employees, departments);

        // إنشاء نافذة الطباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير إجمالي الرواتب - ${this.formatMonth(this.currentMonth)}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .summary-cards { display: flex; justify-content: space-between; margin-bottom: 30px; }
                    .card { border: 1px solid #ddd; padding: 15px; text-align: center; flex: 1; margin: 0 5px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f5f5f5; }
                    .text-center { text-align: center; }
                    .text-end { text-align: left; }
                    .fw-bold { font-weight: bold; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير إجمالي الرواتب</h1>
                    <h2>${this.formatMonth(this.currentMonth)}</h2>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div class="summary-cards">
                    <div class="card">
                        <h3>${summary.totalEmployees}</h3>
                        <p>عدد الموظفين</p>
                    </div>
                    <div class="card">
                        <h3>${window.samApp.formatCurrency(summary.totalGrossSalary)}</h3>
                        <p>إجمالي الاستحقاقات</p>
                    </div>
                    <div class="card">
                        <h3>${window.samApp.formatCurrency(summary.totalDeductions)}</h3>
                        <p>إجمالي الخصومات</p>
                    </div>
                    <div class="card">
                        <h3>${window.samApp.formatCurrency(summary.totalNetSalary)}</h3>
                        <p>إجمالي صافي الرواتب</p>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الحوافز</th>
                            <th>إجمالي الاستحقاقات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${monthPayrolls.map(payroll => {
                            const employee = employees.find(e => e.id === payroll.employee_id);
                            const department = departments.find(d => d.id === employee?.department);
                            const allowances = this.calculateAllowances(payroll);
                            const bonuses = this.calculateBonuses(payroll);

                            return `
                                <tr>
                                    <td>${employee?.name || 'غير معروف'}</td>
                                    <td>${department?.name || 'غير محدد'}</td>
                                    <td class="text-end">${window.samApp.formatCurrency(payroll.basic_salary || 0)}</td>
                                    <td class="text-end">${window.samApp.formatCurrency(allowances)}</td>
                                    <td class="text-end">${window.samApp.formatCurrency(bonuses)}</td>
                                    <td class="text-end fw-bold">${window.samApp.formatCurrency(payroll.gross_salary || 0)}</td>
                                    <td class="text-end">${window.samApp.formatCurrency(payroll.total_deductions || 0)}</td>
                                    <td class="text-end fw-bold">${window.samApp.formatCurrency(payroll.net_salary || 0)}</td>
                                    <td class="text-center">${payroll.status}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                    <tfoot>
                        <tr style="background-color: #f8f9fa; font-weight: bold;">
                            <td>الإجمالي (${summary.totalEmployees} موظف)</td>
                            <td></td>
                            <td class="text-end">${window.samApp.formatCurrency(summary.totalBasicSalary)}</td>
                            <td class="text-end">${window.samApp.formatCurrency(summary.totalAllowances)}</td>
                            <td class="text-end">${window.samApp.formatCurrency(summary.totalBonuses)}</td>
                            <td class="text-end fw-bold">${window.samApp.formatCurrency(summary.totalGrossSalary)}</td>
                            <td class="text-end">${window.samApp.formatCurrency(summary.totalDeductions)}</td>
                            <td class="text-end fw-bold">${window.samApp.formatCurrency(summary.totalNetSalary)}</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }
}